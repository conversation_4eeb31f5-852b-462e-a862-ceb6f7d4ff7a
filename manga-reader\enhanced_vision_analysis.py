import asyncio
import json
import re
from typing import List, Dict, Any
import aiohttp
from enhanced_prompts import CHARACTER_ANALYSIS_PROMPT, ENHANCED_STORY_PROMPT, PREMIUM_VISUAL_PROMPT

class CharacterDatabase:
    """Maintains consistent character names and descriptions throughout the analysis"""
    
    def __init__(self):
        self.characters = {}
        self.name_variations = {}
        
    def add_character(self, name: str, description: str, aliases: List[str] = None):
        """Add a character to the database"""
        canonical_name = self._canonicalize_name(name)
        self.characters[canonical_name] = {
            'name': canonical_name,
            'description': description,
            'aliases': aliases or [],
            'appearances': 0
        }
        
        # Add name variations
        for alias in (aliases or []):
            self.name_variations[alias.lower()] = canonical_name
        self.name_variations[name.lower()] = canonical_name
    
    def _canonicalize_name(self, name: str) -> str:
        """Convert name to canonical form"""
        # Common Naruto character mappings
        naruto_characters = {
            'naruto': '<PERSON><PERSON><PERSON>',
            'sasuke': '<PERSON><PERSON>', 
            'sakura': '<PERSON>',
            'kakashi': '<PERSON><PERSON><PERSON>',
            'rock lee': '<PERSON> Lee',
            'lee': '<PERSON>',
            'gaara': 'Gaara',
            'neji': 'Neji Hyuga',
            'hinata': 'Hinata Hyuga',
            'shikamaru': 'Shikamaru Nara',
            'choji': 'Choji Akimichi',
            'ino': 'Ino Yamanaka',
            'kiba': 'Kiba Inuzuka',
            'shino': 'Shino Aburame',
            'tenten': 'Tenten',
            'guy': 'Might Guy',
            'orochimaru': 'Orochimaru',
            'jiraiya': 'Jiraiya',
            'tsunade': 'Tsunade'
        }
        
        name_lower = name.lower().strip()
        return naruto_characters.get(name_lower, name.title())
    
    def get_canonical_name(self, name: str) -> str:
        """Get the canonical name for a character"""
        name_lower = name.lower().strip()
        if name_lower in self.name_variations:
            return self.name_variations[name_lower]
        return self._canonicalize_name(name)
    
    def record_appearance(self, name: str):
        """Record that a character appeared in a scene"""
        canonical = self.get_canonical_name(name)
        if canonical in self.characters:
            self.characters[canonical]['appearances'] += 1

class EnhancedVisionAnalyzer:
    """Enhanced vision analysis with character recognition and consistency"""
    
    def __init__(self, lm_studio_url: str = "http://localhost:1234"):
        self.lm_studio_url = lm_studio_url
        self.character_db = CharacterDatabase()
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def analyze_characters_in_segment(self, base64_images: List[str]) -> Dict[str, Any]:
        """Analyze characters in a segment of manga pages"""
        
        # Prepare images for analysis
        image_messages = []
        for i, img_b64 in enumerate(base64_images[:6]):  # Limit to 6 images for analysis
            image_messages.append({
                "type": "image_url",
                "image_url": {"url": f"data:image/png;base64,{img_b64}"}
            })
        
        # Character analysis request
        character_payload = {
            "model": "qwen2-vl-2b-instruct",
            "messages": [
                {
                    "role": "user", 
                    "content": [
                        {"type": "text", "text": CHARACTER_ANALYSIS_PROMPT}
                    ] + image_messages
                }
            ],
            "temperature": 0.3,
            "max_tokens": 1000
        }
        
        try:
            async with self.session.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json=character_payload,
                timeout=aiohttp.ClientTimeout(total=120)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    character_analysis = result['choices'][0]['message']['content']
                    
                    # Extract and process character information
                    characters = self._extract_characters(character_analysis)
                    
                    return {
                        'characters': characters,
                        'analysis': character_analysis,
                        'scene_summary': self._extract_scene_summary(character_analysis)
                    }
                else:
                    print(f"Character analysis failed: {response.status}")
                    return {'characters': [], 'analysis': '', 'scene_summary': ''}
                    
        except Exception as e:
            print(f"Error in character analysis: {e}")
            return {'characters': [], 'analysis': '', 'scene_summary': ''}
    
    def _extract_characters(self, analysis: str) -> List[Dict[str, str]]:
        """Extract character information from analysis"""
        characters = []
        
        # Look for character sections
        character_pattern = r"- Character Name: (.+?)\n.*?Description: (.+?)\n.*?Role: (.+?)\n"
        matches = re.findall(character_pattern, analysis, re.DOTALL)
        
        for name, description, role in matches:
            canonical_name = self.character_db.get_canonical_name(name.strip())
            self.character_db.record_appearance(canonical_name)
            
            characters.append({
                'name': canonical_name,
                'description': description.strip(),
                'role': role.strip()
            })
        
        return characters
    
    def _extract_scene_summary(self, analysis: str) -> str:
        """Extract scene summary from analysis"""
        summary_match = re.search(r"\*\*SCENE SUMMARY:\*\*\s*(.+?)(?:\n\n|\Z)", analysis, re.DOTALL)
        if summary_match:
            return summary_match.group(1).strip()
        return ""
    
    async def create_enhanced_narration(self, character_analysis: Dict[str, Any], 
                                      pages_context: str) -> str:
        """Create enhanced narration with character consistency"""
        
        # Build character context
        character_context = ""
        if character_analysis['characters']:
            character_context = "Characters in this scene:\n"
            for char in character_analysis['characters']:
                character_context += f"- {char['name']}: {char['role']}\n"
        
        # Create narration prompt
        narration_prompt = ENHANCED_STORY_PROMPT.format(
            character_analysis=character_analysis['analysis'],
            pages_context=pages_context
        )
        
        payload = {
            "model": "qwen2-vl-2b-instruct", 
            "messages": [
                {
                    "role": "user",
                    "content": narration_prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 500
        }
        
        try:
            async with self.session.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    narration = result['choices'][0]['message']['content']
                    
                    # Clean up narration
                    narration = self._clean_narration(narration)
                    return narration
                else:
                    print(f"Narration generation failed: {response.status}")
                    return f"In this scene, {character_analysis['scene_summary']}"
                    
        except Exception as e:
            print(f"Error generating narration: {e}")
            return f"In this scene, {character_analysis['scene_summary']}"
    
    def _clean_narration(self, narration: str) -> str:
        """Clean and optimize narration for voice synthesis"""
        # Remove markdown formatting
        narration = re.sub(r'\*\*(.+?)\*\*', r'\1', narration)
        narration = re.sub(r'\*(.+?)\*', r'\1', narration)
        
        # Remove extra whitespace
        narration = ' '.join(narration.split())
        
        # Ensure proper sentence endings
        if not narration.endswith(('.', '!', '?')):
            narration += '.'
            
        return narration
    
    def get_character_summary(self) -> Dict[str, Any]:
        """Get summary of all characters found"""
        return {
            'total_characters': len(self.character_db.characters),
            'characters': self.character_db.characters
        }

async def analyze_manga_segment_enhanced(base64_images: List[str], 
                                       segment_index: int,
                                       lm_studio_url: str = "http://localhost:1234") -> Dict[str, Any]:
    """Enhanced analysis of a manga segment with character recognition"""
    
    async with EnhancedVisionAnalyzer(lm_studio_url) as analyzer:
        # Analyze characters
        character_analysis = await analyzer.analyze_characters_in_segment(base64_images)
        
        # Create enhanced narration
        pages_context = f"Manga segment {segment_index + 1} with {len(base64_images)} pages"
        narration = await analyzer.create_enhanced_narration(character_analysis, pages_context)
        
        return {
            'segment_index': segment_index,
            'narration': narration,
            'characters': character_analysis['characters'],
            'scene_summary': character_analysis['scene_summary'],
            'character_analysis': character_analysis['analysis'],
            'total_pages': len(base64_images)
        }
