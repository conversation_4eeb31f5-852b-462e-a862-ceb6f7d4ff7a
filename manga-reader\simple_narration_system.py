import asyncio
import aiohttp
import json
import re
from typing import List, Dict, Any
import google.generativeai as genai
import os

class SimpleNarrationSystem:
    """Simple and effective narration system using LM Studio + Gemini"""
    
    def __init__(self, lm_studio_url: str = "http://localhost:1234", gemini_api_key: str = None):
        self.lm_studio_url = lm_studio_url
        self.session = None
        
        # Configure Gemini
        if gemini_api_key:
            genai.configure(api_key=gemini_api_key)
            self.gemini_model = genai.GenerativeModel('gemini-pro')
        else:
            # Try to get from environment
            api_key = os.getenv('GEMINI_API_KEY')
            if api_key:
                genai.configure(api_key=api_key)
                self.gemini_model = genai.GenerativeModel('gemini-pro')
            else:
                print("⚠️ No Gemini API key found. Using LM Studio for narration (less optimal)")
                self.gemini_model = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def describe_single_page(self, base64_image: str, page_number: int) -> Dict[str, Any]:
        """Use LM Studio to describe a single page and extract text"""
        
        simple_prompt = f"""
Analyze this manga page (Page {page_number}) and provide:

1. SCENE DESCRIPTION: What is happening in this page? Who are the characters? What actions are taking place?

2. TEXT CONTENT: What text/dialogue appears in speech bubbles or text boxes? Write it exactly as it appears.

3. KEY ELEMENTS: Any important visual elements, emotions, or story moments.

Be clear and concise. Focus on what's actually visible in the page.

Format your response as:
SCENE: [description]
TEXT: [any text/dialogue found]
ELEMENTS: [key visual elements]
"""
        
        payload = {
            "model": "qwen2-vl-2b-instruct",
            "messages": [
                {
                    "role": "user", 
                    "content": [
                        {"type": "text", "text": simple_prompt},
                        {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
                    ]
                }
            ],
            "temperature": 0.3,
            "max_tokens": 500
        }
        
        try:
            async with self.session.post(
                f"{self.lm_studio_url}/v1/chat/completions",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    description = result['choices'][0]['message']['content']
                    
                    # Parse the response
                    scene_match = re.search(r'SCENE:\s*(.+?)(?=TEXT:|ELEMENTS:|$)', description, re.DOTALL)
                    text_match = re.search(r'TEXT:\s*(.+?)(?=ELEMENTS:|$)', description, re.DOTALL)
                    elements_match = re.search(r'ELEMENTS:\s*(.+?)$', description, re.DOTALL)
                    
                    return {
                        'page_number': page_number,
                        'scene': scene_match.group(1).strip() if scene_match else "Scene continues",
                        'text': text_match.group(1).strip() if text_match else "No text visible",
                        'elements': elements_match.group(1).strip() if elements_match else "Visual elements present",
                        'raw_description': description
                    }
                else:
                    print(f"   ⚠️ LM Studio error for page {page_number}: {response.status}")
                    return {
                        'page_number': page_number,
                        'scene': f"Action continues on page {page_number}",
                        'text': "No text detected",
                        'elements': "Visual story elements",
                        'raw_description': "Error in analysis"
                    }
                    
        except Exception as e:
            print(f"   ❌ Error analyzing page {page_number}: {e}")
            return {
                'page_number': page_number,
                'scene': f"Story continues on page {page_number}",
                'text': "No text detected",
                'elements': "Visual story elements",
                'raw_description': "Error in analysis"
            }
    
    async def analyze_all_pages(self, base64_images: List[str]) -> List[Dict[str, Any]]:
        """Analyze all pages with LM Studio vision"""
        
        print(f"🔍 Analyzing {len(base64_images)} pages with LM Studio...")
        
        # Limit concurrent requests to avoid overwhelming LM Studio
        semaphore = asyncio.Semaphore(3)
        
        async def analyze_page_with_semaphore(base64_image, page_num):
            async with semaphore:
                return await self.describe_single_page(base64_image, page_num + 1)
        
        # Analyze all pages in parallel
        tasks = [
            analyze_page_with_semaphore(img, i) 
            for i, img in enumerate(base64_images)
        ]
        
        page_descriptions = await asyncio.gather(*tasks)
        
        print(f"✅ Analyzed all {len(page_descriptions)} pages")
        return page_descriptions
    
    def create_gemini_prompt(self, page_descriptions: List[Dict[str, Any]]) -> str:
        """Create a comprehensive prompt for Gemini"""
        
        prompt = """You are a professional manga narrator creating engaging narrations for a manga recap video.

I will provide you with detailed descriptions of each manga page. For each page, create a 2-3 line narration that:
1. Captures the key action/emotion of that specific page
2. Incorporates any dialogue or text from that page
3. Flows naturally when spoken aloud
4. Builds excitement and engagement

IMPORTANT: 
- Provide narration for EACH page individually
- Keep each narration to 2-3 lines maximum
- Make it engaging and dramatic
- Include character names when clear
- Reference the dialogue/text when present

Here are the page descriptions:

"""
        
        for desc in page_descriptions:
            prompt += f"""
PAGE {desc['page_number']}:
Scene: {desc['scene']}
Text/Dialogue: {desc['text']}
Key Elements: {desc['elements']}

"""
        
        prompt += """
Now provide narrations in this exact format:

PAGE 1: [Your 2-3 line narration for page 1]
PAGE 2: [Your 2-3 line narration for page 2]
PAGE 3: [Your 2-3 line narration for page 3]
...and so on for all pages.

Make each narration exciting and perfect for voice synthesis!"""
        
        return prompt
    
    async def generate_narrations_with_gemini(self, page_descriptions: List[Dict[str, Any]]) -> Dict[int, str]:
        """Generate narrations using Gemini"""
        
        if not self.gemini_model:
            print("⚠️ Gemini not available, using fallback narrations")
            return self._create_fallback_narrations(page_descriptions)
        
        print(f"🤖 Generating narrations with Gemini for {len(page_descriptions)} pages...")
        
        try:
            prompt = self.create_gemini_prompt(page_descriptions)
            
            response = await asyncio.get_event_loop().run_in_executor(
                None, 
                lambda: self.gemini_model.generate_content(prompt)
            )
            
            narrations_text = response.text
            
            # Parse the narrations
            narrations = {}
            lines = narrations_text.split('\n')
            
            for line in lines:
                line = line.strip()
                if line.startswith('PAGE '):
                    # Extract page number and narration
                    match = re.match(r'PAGE (\d+):\s*(.+)', line)
                    if match:
                        page_num = int(match.group(1))
                        narration = match.group(2).strip()
                        narrations[page_num] = narration
            
            print(f"✅ Generated {len(narrations)} narrations with Gemini")
            
            # Fill in any missing narrations
            for desc in page_descriptions:
                page_num = desc['page_number']
                if page_num not in narrations:
                    narrations[page_num] = f"In this scene, {desc['scene'][:50]}..."
            
            return narrations
            
        except Exception as e:
            print(f"❌ Error with Gemini: {e}")
            return self._create_fallback_narrations(page_descriptions)
    
    def _create_fallback_narrations(self, page_descriptions: List[Dict[str, Any]]) -> Dict[int, str]:
        """Create fallback narrations when Gemini is not available"""
        narrations = {}
        
        for desc in page_descriptions:
            page_num = desc['page_number']
            scene = desc['scene']
            text = desc['text']
            
            # Create simple but effective narration
            if text and text != "No text visible" and text != "No text detected":
                narration = f"On page {page_num}, {scene[:30]}... The dialogue reveals: {text[:40]}..."
            else:
                narration = f"Page {page_num} shows {scene[:50]}... The action intensifies as the story unfolds."
            
            narrations[page_num] = narration
        
        return narrations
    
    async def create_simple_narrations(self, base64_images: List[str]) -> Dict[int, str]:
        """Main function to create simple, effective narrations"""
        
        # Step 1: Analyze all pages with LM Studio
        page_descriptions = await self.analyze_all_pages(base64_images)
        
        # Step 2: Generate narrations with Gemini
        narrations = await self.generate_narrations_with_gemini(page_descriptions)
        
        return narrations

# Main function for simple narration
async def create_simple_manga_narrations(base64_images: List[str], 
                                       gemini_api_key: str = None) -> Dict[int, str]:
    """Create simple, effective manga narrations"""
    
    async with SimpleNarrationSystem(gemini_api_key=gemini_api_key) as narrator:
        return await narrator.create_simple_narrations(base64_images)
