#!/usr/bin/env python3
"""
Premium Manga Recap System
Enhanced with character recognition, premium visual effects, and perfect audio sync
"""

import asyncio
import argparse
from pathlib import Path
from typing import List, Dict, Any
import sys

# Import existing modules
from manga_extraction import extract_all_pages_as_images
from enhanced_movie_director import create_enhanced_manga_movie

def print_banner():
    """Print premium app banner"""
    print("🎬" + "="*60 + "🎬")
    print("🌟           PREMIUM MANGA RECAP SYSTEM           🌟")
    print("🎭 Character Recognition • Premium Effects • Perfect Sync 🎭")
    print("🤖 LM Studio + Qwen Vision • Edge-TTS Narration 🗣️")
    print("💰                100% FREE                      💰")
    print("🎬" + "="*60 + "🎬")

def create_segments_from_pages(base64_images: List[str], pages_per_segment: int = 12) -> List[Dict[str, Any]]:
    """Create segments from extracted pages"""
    segments = []
    total_pages = len(base64_images)
    
    print(f"📚 Creating segments from {total_pages} pages ({pages_per_segment} pages per segment)...")
    
    for i in range(0, total_pages, pages_per_segment):
        end_idx = min(i + pages_per_segment, total_pages)
        segment_images = base64_images[i:end_idx]
        
        segment = {
            'segment_index': len(segments),
            'base64_images': segment_images,
            'start_page': i,
            'end_page': end_idx - 1,
            'total_pages': len(segment_images)
        }
        
        segments.append(segment)
        print(f"   📖 Segment {len(segments)}: Pages {i}-{end_idx-1} ({len(segment_images)} pages)")
    
    print(f"✅ Created {len(segments)} segments")
    return segments

async def create_premium_manga_recap(pdf_path: str, output_name: str) -> bool:
    """Create premium manga recap with all enhancements"""
    
    try:
        print_banner()
        print(f"\n🎬 Starting Premium Manga Recap Creation")
        print(f"📖 Processing: {pdf_path}")
        print(f"🎯 Output: {output_name}")
        print(f"🔧 Make sure LM Studio is running on http://localhost:1234")
        print(f"🗣️ Using Edge-TTS for premium narration")
        
        # Step 1: Extract pages
        print(f"\n📄 Step 1: Extracting pages from PDF...")
        base64_images = extract_all_pages_as_images(pdf_path)
        
        if not base64_images:
            print("❌ No pages extracted from PDF")
            return False
        
        print(f"✅ Extracted {len(base64_images)} pages successfully")
        
        # Step 2: Create optimized segments
        print(f"\n🔍 Step 2: Creating optimized story segments...")
        segments = create_segments_from_pages(base64_images, pages_per_segment=10)  # Smaller segments for better analysis
        
        if not segments:
            print("❌ No segments created")
            return False
        
        print(f"✅ Created {len(segments)} optimized segments")
        
        # Step 3: Enhanced analysis and video creation
        print(f"\n🎨 Step 3: Creating premium recap with enhanced features...")
        print("   🤖 Character recognition and consistency")
        print("   🎭 Premium visual effects with blurred backgrounds")
        print("   🎵 Perfect audio-video synchronization")
        print("   ✨ Smooth fade transitions between scenes")
        
        # Prepare output directory
        output_dir = Path(output_name).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create enhanced movie
        video_path = await create_enhanced_manga_movie(
            segments, 
            str(output_dir), 
            Path(output_name).stem, 
            None
        )
        
        if video_path and Path(video_path).exists():
            print(f"\n🎉 SUCCESS! Premium manga recap created!")
            print(f"📁 Video location: {video_path}")
            print(f"💰 Total cost: $0.00 (FREE with LM Studio + Edge-TTS!)")
            print(f"\n🌟 Premium Features Applied:")
            print(f"   ✅ Character recognition and consistent naming")
            print(f"   ✅ Blurred background effects")
            print(f"   ✅ Smooth fade transitions")
            print(f"   ✅ Perfect audio-video synchronization")
            print(f"   ✅ Professional quality output")
            return True
        else:
            print(f"❌ Video creation failed - file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error creating premium manga recap: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Premium Manga Recap System - Create professional manga recap videos",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python premium_manga_app.py manga.pdf --output outputs/my_recap
  python premium_manga_app.py naruto/v10/v10.pdf --output outputs/naruto_v10_premium

Features:
  • Character Recognition: Consistent character names throughout
  • Premium Visual Effects: Blurred backgrounds, fade transitions
  • Perfect Audio Sync: Video timing matches narration perfectly
  • Professional Quality: High-resolution output with premium styling
  • 100% FREE: Uses LM Studio + Edge-TTS (no API costs)
        """
    )
    
    parser.add_argument(
        "pdf_path", 
        help="Path to the manga PDF file"
    )
    
    parser.add_argument(
        "--output", 
        required=True,
        help="Output name/path for the recap video (without extension)"
    )
    
    parser.add_argument(
        "--pages-per-segment",
        type=int,
        default=10,
        help="Number of pages per segment (default: 10, smaller = better analysis)"
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    pdf_path = Path(args.pdf_path)
    if not pdf_path.exists():
        print(f"❌ PDF file not found: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.suffix.lower() == '.pdf':
        print(f"❌ File must be a PDF: {pdf_path}")
        sys.exit(1)
    
    # Run the premium recap creation
    try:
        success = asyncio.run(create_premium_manga_recap(str(pdf_path), args.output))
        if success:
            print(f"\n🎊 Premium manga recap completed successfully! 🎊")
            sys.exit(0)
        else:
            print(f"\n💥 Premium manga recap failed! 💥")
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⏹️ Process cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
