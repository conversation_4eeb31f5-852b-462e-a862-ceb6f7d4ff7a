import numpy as np
from PIL import Image, ImageFilter, ImageEnhance
from io import BytesIO
import base64
from moviepy import ImageClip, CompositeVideoClip, concatenate_videoclips, vfx
from typing import List

class PremiumVideoEffects:
    """Premium video effects for manga recap videos"""
    
    def __init__(self, target_width=1920, target_height=1080):
        self.target_width = target_width
        self.target_height = target_height
        self.aspect_ratio = target_width / target_height
    
    def create_blurred_background(self, image_data: bytes, blur_radius: int = 15) -> Image.Image:
        """Create a blurred background from the manga page"""
        # Load image
        image = Image.open(BytesIO(image_data))
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize to target resolution
        background = image.resize((self.target_width, self.target_height), Image.Resampling.LANCZOS)
        
        # Apply blur effect
        background = background.filter(ImageFilter.GaussianBlur(radius=blur_radius))
        
        # Darken the background slightly
        enhancer = ImageEnhance.Brightness(background)
        background = enhancer.enhance(0.6)  # Make it 60% as bright
        
        return background
    
    def create_foreground_page(self, image_data: bytes, max_scale: float = 0.8) -> Image.Image:
        """Create the foreground manga page with proper scaling"""
        # Load image
        image = Image.open(BytesIO(image_data))
        
        # Convert to RGB if needed
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Calculate scaling to fit within max_scale of screen
        img_width, img_height = image.size
        img_aspect = img_width / img_height
        
        # Determine target size
        if img_aspect > self.aspect_ratio:
            # Image is wider - fit to width
            target_width = int(self.target_width * max_scale)
            target_height = int(target_width / img_aspect)
        else:
            # Image is taller - fit to height
            target_height = int(self.target_height * max_scale)
            target_width = int(target_height * img_aspect)
        
        # Resize image
        foreground = image.resize((target_width, target_height), Image.Resampling.LANCZOS)
        
        # Add subtle shadow/border effect
        foreground = self._add_shadow_effect(foreground)
        
        return foreground
    
    def _add_shadow_effect(self, image: Image.Image) -> Image.Image:
        """Add a subtle shadow effect to the image"""
        # Create a slightly larger canvas
        shadow_offset = 10
        new_width = image.width + shadow_offset * 2
        new_height = image.height + shadow_offset * 2
        
        # Create shadow
        shadow = Image.new('RGBA', (new_width, new_height), (0, 0, 0, 0))
        shadow_img = Image.new('RGBA', image.size, (0, 0, 0, 100))  # Semi-transparent black
        shadow.paste(shadow_img, (shadow_offset + 3, shadow_offset + 3))  # Offset shadow
        
        # Blur the shadow
        shadow = shadow.filter(ImageFilter.GaussianBlur(radius=5))
        
        # Paste original image on top
        if image.mode != 'RGBA':
            image = image.convert('RGBA')
        shadow.paste(image, (shadow_offset, shadow_offset), image)
        
        return shadow
    
    def create_composite_frame(self, image_data: bytes) -> Image.Image:
        """Create a composite frame with blurred background and foreground page"""
        # Create blurred background
        background = self.create_blurred_background(image_data)
        
        # Create foreground page
        foreground = self.create_foreground_page(image_data)
        
        # Create composite
        composite = Image.new('RGB', (self.target_width, self.target_height))
        composite.paste(background, (0, 0))
        
        # Center the foreground
        fg_x = (self.target_width - foreground.width) // 2
        fg_y = (self.target_height - foreground.height) // 2
        
        if foreground.mode == 'RGBA':
            composite.paste(foreground, (fg_x, fg_y), foreground)
        else:
            composite.paste(foreground, (fg_x, fg_y))
        
        return composite
    
    def create_premium_video_clip(self, base64_images: List[str], audio_duration: float) -> ImageClip:
        """Create a premium video clip with effects"""
        if not base64_images:
            return None
        
        # Calculate timing
        images_per_second = len(base64_images) / audio_duration
        min_duration_per_image = 1.0  # Minimum 1 second per image
        
        if images_per_second > 1.0:
            # Too many images - select key frames
            step = max(1, len(base64_images) // int(audio_duration))
            selected_images = base64_images[::step]
        else:
            selected_images = base64_images
        
        # Ensure we have at least some images
        if not selected_images:
            selected_images = [base64_images[0]]
        
        # Calculate duration per image
        duration_per_image = audio_duration / len(selected_images)
        duration_per_image = max(min_duration_per_image, duration_per_image)
        
        # Create clips with effects
        clips = []
        fade_duration = 0.5  # 0.5 second fade
        
        for i, img_b64 in enumerate(selected_images):
            try:
                # Decode image
                image_data = base64.b64decode(img_b64)
                
                # Create composite frame
                composite_frame = self.create_composite_frame(image_data)
                
                # Convert to numpy array for MoviePy
                frame_array = np.array(composite_frame)
                
                # Create clip
                clip = ImageClip(frame_array).set_duration(duration_per_image)
                
                # Add fade effects
                if i == 0:
                    # First clip - fade in only
                    clip = clip.with_effects([vfx.FadeIn(fade_duration)])
                elif i == len(selected_images) - 1:
                    # Last clip - fade out only
                    clip = clip.with_effects([vfx.FadeOut(fade_duration)])
                else:
                    # Middle clips - crossfade effect will be handled in concatenation
                    pass
                
                clips.append(clip)
                
            except Exception as e:
                print(f"Error creating clip for image {i}: {e}")
                continue
        
        if not clips:
            return None
        
        # Concatenate with crossfade
        if len(clips) == 1:
            final_clip = clips[0]
        else:
            # Create crossfade transitions
            final_clips = [clips[0]]
            
            for i in range(1, len(clips)):
                # Add crossfade transition
                prev_clip = final_clips[-1]
                curr_clip = clips[i]
                
                # Adjust timing for crossfade
                crossfade_duration = min(0.3, prev_clip.duration / 3, curr_clip.duration / 3)
                
                # Modify previous clip to fade out
                prev_clip = prev_clip.with_effects([vfx.FadeOut(crossfade_duration)])

                # Modify current clip to fade in and start earlier
                curr_clip = curr_clip.with_effects([vfx.FadeIn(crossfade_duration)]).set_start(
                    prev_clip.duration - crossfade_duration
                )
                
                final_clips[-1] = prev_clip
                final_clips.append(curr_clip)
            
            # Composite all clips
            final_clip = CompositeVideoClip(final_clips)
        
        # Ensure the clip matches the audio duration
        final_clip = final_clip.set_duration(audio_duration)
        
        return final_clip
    
    def add_text_overlay(self, clip: ImageClip, text: str, position: str = 'bottom') -> CompositeVideoClip:
        """Add text overlay to video clip"""
        from moviepy.editor import TextClip
        
        try:
            # Create text clip
            txt_clip = TextClip(
                text,
                fontsize=24,
                color='white',
                font='Arial-Bold',
                stroke_color='black',
                stroke_width=2
            ).set_duration(clip.duration)
            
            # Position text
            if position == 'bottom':
                txt_clip = txt_clip.set_position(('center', 'bottom')).set_margin(20)
            elif position == 'top':
                txt_clip = txt_clip.set_position(('center', 'top')).set_margin(20)
            else:
                txt_clip = txt_clip.set_position('center')
            
            # Composite
            return CompositeVideoClip([clip, txt_clip])
            
        except Exception as e:
            print(f"Error adding text overlay: {e}")
            return clip

def create_premium_manga_clip(base64_images: List[str], audio_duration: float, 
                            segment_index: int = 0) -> ImageClip:
    """Create a premium manga video clip with all effects"""
    effects = PremiumVideoEffects()
    
    print(f"Creating premium clip for segment {segment_index} with {len(base64_images)} images, duration: {audio_duration:.2f}s")
    
    clip = effects.create_premium_video_clip(base64_images, audio_duration)
    
    if clip is None:
        print(f"Failed to create clip for segment {segment_index}")
        return None
    
    print(f"✅ Premium clip created for segment {segment_index}")
    return clip
