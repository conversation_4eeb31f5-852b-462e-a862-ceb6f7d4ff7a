import asyncio
import os
import base64
from io import BytesIO
from typing import List, Dict, Any
import edge_tts
from moviepy import AudioFileClip, concatenate_videoclips, concatenate_audioclips
from enhanced_vision_analysis import analyze_manga_segment_enhanced
from premium_video_effects import create_premium_manga_clip

class EnhancedMovieDirector:
    """Enhanced movie director with character recognition and premium effects"""
    
    def __init__(self, voice: str = "en-US-AriaNeural"):
        self.voice = voice
        self.character_database = {}
        self.total_segments = 0
        
    async def create_enhanced_movie(self, movie_script: List[Dict[str, Any]], 
                                  output_dir: str, movie_name: str, 
                                  volume_number: str = None) -> str:
        """Create enhanced movie with character recognition and premium effects"""
        
        print("🎬 Creating Enhanced Manga Recap with Premium Effects...")
        print(f"📊 Processing {len(movie_script)} segments")
        
        # Step 1: Enhanced character analysis and narration
        print("\n🤖 Step 1: Enhanced Character Analysis & Narration...")
        enhanced_script = await self._enhance_script_with_character_analysis(movie_script)
        
        # Step 2: Generate premium narration audio
        print("\n🗣️ Step 2: Generating Premium Narration...")
        await self._generate_enhanced_audio(enhanced_script)
        
        # Step 3: Create premium video with effects
        print("\n🎨 Step 3: Creating Premium Video with Effects...")
        video_clips = await self._create_premium_video_clips(enhanced_script, output_dir)
        
        # Step 4: Combine everything
        print("\n🎞️ Step 4: Final Video Assembly...")
        final_video_path = await self._assemble_final_video(
            video_clips, enhanced_script, output_dir, movie_name, volume_number
        )
        
        # Step 5: Character summary
        self._print_character_summary()
        
        return final_video_path
    
    async def _enhance_script_with_character_analysis(self, movie_script: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance script with character analysis"""
        enhanced_script = []
        
        for i, segment in enumerate(movie_script):
            print(f"   Analyzing segment {i+1}/{len(movie_script)}...")
            
            # Get base64 images
            base64_images = segment.get('base64_images', [])
            
            if not base64_images:
                print(f"   ⚠️ No images in segment {i+1}, skipping...")
                continue
            
            # Enhanced analysis with character recognition
            try:
                enhanced_analysis = await analyze_manga_segment_enhanced(
                    base64_images, i
                )
                
                # Update character database
                for character in enhanced_analysis.get('characters', []):
                    char_name = character['name']
                    if char_name not in self.character_database:
                        self.character_database[char_name] = {
                            'appearances': 0,
                            'descriptions': []
                        }
                    self.character_database[char_name]['appearances'] += 1
                    self.character_database[char_name]['descriptions'].append(
                        character.get('description', '')
                    )
                
                # Create enhanced segment
                enhanced_segment = {
                    'segment_index': i,
                    'base64_images': base64_images,
                    'enhanced_narration': enhanced_analysis['narration'],
                    'characters': enhanced_analysis['characters'],
                    'scene_summary': enhanced_analysis['scene_summary'],
                    'total_pages': len(base64_images),
                    'narration_audio': None  # Will be filled later
                }
                
                enhanced_script.append(enhanced_segment)
                print(f"   ✅ Enhanced segment {i+1} - Found {len(enhanced_analysis['characters'])} characters")
                
            except Exception as e:
                print(f"   ❌ Error enhancing segment {i+1}: {e}")
                # Fallback to original
                enhanced_segment = {
                    'segment_index': i,
                    'base64_images': base64_images,
                    'enhanced_narration': segment.get('text', 'In this manga scene, the story continues...'),
                    'characters': [],
                    'scene_summary': 'Action continues in this scene.',
                    'total_pages': len(base64_images),
                    'narration_audio': None
                }
                enhanced_script.append(enhanced_segment)
        
        print(f"✅ Enhanced {len(enhanced_script)} segments with character analysis")
        return enhanced_script
    
    async def _generate_enhanced_audio(self, enhanced_script: List[Dict[str, Any]]):
        """Generate enhanced audio with proper pacing"""
        
        # Semaphore to limit concurrent audio generation
        semaphore = asyncio.Semaphore(3)
        
        async def generate_segment_audio(segment):
            async with semaphore:
                try:
                    narration_text = segment['enhanced_narration']
                    
                    # Add natural pauses for better pacing
                    narration_text = self._add_natural_pauses(narration_text)
                    
                    # Generate audio
                    communicate = edge_tts.Communicate(narration_text, self.voice)
                    
                    audio_bytes_io = BytesIO()
                    async for chunk in communicate.stream():
                        if chunk["type"] == "audio":
                            audio_bytes_io.write(chunk["data"])
                    
                    audio_bytes_io.seek(0)
                    segment['narration_audio'] = audio_bytes_io
                    
                    # Calculate duration for pacing
                    audio_size = audio_bytes_io.getbuffer().nbytes
                    estimated_duration = audio_size / 32000  # Rough estimation
                    segment['estimated_audio_duration'] = estimated_duration
                    
                    print(f"   ✅ Generated audio for segment {segment['segment_index']+1} - {audio_size} bytes (~{estimated_duration:.1f}s)")
                    
                except Exception as e:
                    print(f"   ❌ Error generating audio for segment {segment['segment_index']+1}: {e}")
                    segment['narration_audio'] = BytesIO()
                    segment['estimated_audio_duration'] = 5.0
        
        # Generate all audio concurrently
        await asyncio.gather(*[generate_segment_audio(segment) for segment in enhanced_script])
        
        print(f"✅ Generated audio for all {len(enhanced_script)} segments")
    
    def _add_natural_pauses(self, text: str) -> str:
        """Add natural pauses to narration for better pacing"""
        # Add pauses after sentences
        text = text.replace('. ', '. <break time="0.5s"/> ')
        text = text.replace('! ', '! <break time="0.5s"/> ')
        text = text.replace('? ', '? <break time="0.5s"/> ')
        
        # Add pauses after commas
        text = text.replace(', ', ', <break time="0.3s"/> ')
        
        # Add emphasis pauses
        text = text.replace(' and ', ' <break time="0.2s"/> and <break time="0.2s"/> ')
        text = text.replace(' but ', ' <break time="0.2s"/> but <break time="0.2s"/> ')
        
        return text
    
    async def _create_premium_video_clips(self, enhanced_script: List[Dict[str, Any]], 
                                        output_dir: str) -> List:
        """Create premium video clips with effects"""
        video_clips = []
        audio_clips = []
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        for segment in enhanced_script:
            segment_index = segment['segment_index']
            base64_images = segment['base64_images']
            narration_audio = segment['narration_audio']
            
            print(f"   Creating premium clip for segment {segment_index+1}...")
            
            try:
                # Save audio to temporary file
                temp_audio_path = f"{output_dir}/temp_audio_{segment_index}.mp3"
                with open(temp_audio_path, "wb") as audio_file:
                    audio_file.write(narration_audio.getvalue())
                
                # Load audio to get exact duration
                audio_clip = AudioFileClip(temp_audio_path)
                audio_duration = audio_clip.duration
                audio_clips.append(audio_clip)
                
                print(f"   Audio duration: {audio_duration:.2f}s for {len(base64_images)} images")
                
                # Create premium video clip with effects
                video_clip = create_premium_manga_clip(
                    base64_images, audio_duration, segment_index
                )
                
                if video_clip is not None:
                    # Attach audio to video
                    video_clip = video_clip.with_audio(audio_clip)
                    video_clips.append(video_clip)
                    print(f"   ✅ Created premium clip {segment_index+1} - Duration: {video_clip.duration:.2f}s")
                else:
                    print(f"   ❌ Failed to create video clip for segment {segment_index+1}")
                
            except Exception as e:
                print(f"   ❌ Error creating clip for segment {segment_index+1}: {e}")
                continue
        
        print(f"✅ Created {len(video_clips)} premium video clips")
        return video_clips
    
    async def _assemble_final_video(self, video_clips: List, enhanced_script: List[Dict[str, Any]], 
                                  output_dir: str, movie_name: str, volume_number: str = None) -> str:
        """Assemble final video with all clips"""
        
        if not video_clips:
            raise Exception("No video clips to assemble")
        
        # Concatenate all video clips
        print(f"   Concatenating {len(video_clips)} clips...")
        final_video = concatenate_videoclips(video_clips, method="compose")
        
        # Determine output path
        if volume_number:
            final_path = f"{output_dir}/v{volume_number}/recap.mp4"
            os.makedirs(f"{output_dir}/v{volume_number}", exist_ok=True)
        else:
            final_path = f"{output_dir}/recap.mp4"
        
        # Write final video
        print(f"   Writing final video to: {final_path}")
        final_video.write_videofile(
            final_path,
            codec="libx264",
            audio_codec="aac",
            temp_audiofile=f"{output_dir}/temp-audio.m4a",
            remove_temp=True,
            fps=24,
            preset='medium',  # Good quality/speed balance
            bitrate="5000k"   # High quality bitrate
        )
        
        # Cleanup
        self._cleanup_temp_files(output_dir)
        
        print(f"✅ Final enhanced video created: {final_path}")
        return final_path
    
    def _cleanup_temp_files(self, output_dir: str):
        """Clean up temporary files"""
        import glob
        temp_files = glob.glob(f"{output_dir}/temp_audio_*.mp3")
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
            except:
                pass
    
    def _print_character_summary(self):
        """Print summary of characters found"""
        print(f"\n👥 Character Summary:")
        print(f"   Found {len(self.character_database)} unique characters:")
        
        for char_name, data in sorted(self.character_database.items(), 
                                    key=lambda x: x[1]['appearances'], reverse=True):
            print(f"   - {char_name}: {data['appearances']} appearances")

# Main function for enhanced movie creation
async def create_enhanced_manga_movie(movie_script: List[Dict[str, Any]], 
                                    output_dir: str, movie_name: str, 
                                    volume_number: str = None) -> str:
    """Create enhanced manga movie with all improvements"""
    
    director = EnhancedMovieDirector()
    return await director.create_enhanced_movie(
        movie_script, output_dir, movie_name, volume_number
    )
