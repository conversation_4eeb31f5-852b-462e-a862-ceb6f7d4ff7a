#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API unflatten_dense_tensors {
  using schema = ::std::vector<at::Tensor> (const at::Tensor &, at::TensorList);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::unflatten_dense_tensors";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "unflatten_dense_tensors(Tensor flat, Tensor[] tensors) -> Tensor[]";
  static ::std::vector<at::Tensor> call(const at::Tensor & flat, at::TensorList tensors);
  static ::std::vector<at::Tensor> redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & flat, at::TensorList tensors);
};

}} // namespace at::_ops
