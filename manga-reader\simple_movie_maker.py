#!/usr/bin/env python3
"""
Simplified Movie Maker for Manga Recaps
Takes story segments and creates high-quality videos
"""

import os
import asyncio
import base64
from io import BytesIO
from PIL import Image
import edge_tts
import numpy as np

# Try to import moviepy, fallback if not available
try:
    from moviepy.editor import *
    MOVIEPY_AVAILABLE = True
except ImportError:
    print("⚠️  MoviePy not available. Video creation will be limited.")
    MOVIEPY_AVAILABLE = False

EDGE_TTS_VOICE = "en-US-AriaNeural"

async def create_simple_manga_video(movie_script, output_dir, video_name):
    """
    Create a manga recap video from story segments

    Args:
        movie_script: List of segments with 'text', 'images', 'images_unscaled'
        output_dir: Output directory path
        video_name: Name for the output video (without extension)

    Returns:
        Path to created video file or None if failed
    """

    if not MOVIEPY_AVAILABLE:
        print("❌ MoviePy not available. Cannot create video.")
        return None

    print(f"🎬 Creating manga video: {video_name}")
    print(f"📁 Output directory: {output_dir}")
    print(f"📊 Processing {len(movie_script)} segments")
    
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)
    
    video_clips = []
    temp_audio_files = []
    
    try:
        for segment_idx, segment in enumerate(movie_script):
            print(f"🎭 Processing segment {segment_idx + 1}/{len(movie_script)}")
            
            story_text = segment['text']
            images = segment.get('images_unscaled', segment.get('images', []))
            
            if not images:
                print(f"⚠️  No images in segment {segment_idx + 1}, skipping...")
                continue
            
            # Generate audio for this segment
            print(f"🗣️  Generating narration for segment {segment_idx + 1}...")
            
            try:
                communicate = edge_tts.Communicate(story_text, EDGE_TTS_VOICE)
                
                # Save audio to temporary file
                temp_audio_path = os.path.join(output_dir, f"temp_audio_{segment_idx}.mp3")
                await communicate.save(temp_audio_path)
                temp_audio_files.append(temp_audio_path)
                
                # Load audio to get duration
                audio_clip = AudioFileClip(temp_audio_path)
                audio_duration = audio_clip.duration
                
                print(f"🎵 Generated {audio_duration:.1f}s of audio for segment {segment_idx + 1}")
                
            except Exception as e:
                print(f"❌ Error generating audio for segment {segment_idx + 1}: {e}")
                continue
            
            # Create video clips from images
            print(f"🖼️  Creating video from {len(images)} images...")
            
            try:
                # Calculate time per image
                time_per_image = audio_duration / len(images)
                
                image_clips = []
                
                for img_idx, img_base64 in enumerate(images):
                    # Decode base64 image
                    img_data = base64.b64decode(img_base64)
                    img = Image.open(BytesIO(img_data))
                    
                    # Resize for video (1080p with black bars if needed)
                    img_resized = resize_image_for_video(img)
                    
                    # Create video clip
                    img_clip = ImageClip(np.array(img_resized)).set_duration(time_per_image)
                    image_clips.append(img_clip)
                
                # Concatenate images for this segment
                if image_clips:
                    segment_video = concatenate_videoclips(image_clips)
                    segment_video = segment_video.set_audio(audio_clip)
                    video_clips.append(segment_video)
                    
                    print(f"✅ Created {audio_duration:.1f}s video for segment {segment_idx + 1}")
                
                audio_clip.close()
                
            except Exception as e:
                print(f"❌ Error creating video for segment {segment_idx + 1}: {e}")
                continue
        
        # Combine all segments into final video
        if not video_clips:
            print("❌ No video clips created")
            return None
        
        print(f"🎬 Combining {len(video_clips)} segments into final video...")
        
        final_video = concatenate_videoclips(video_clips)
        
        # Output path
        output_path = os.path.join(output_dir, f"{video_name}.mp4")
        
        # Write final video with high quality
        print(f"💾 Writing video to: {output_path}")
        
        final_video.write_videofile(
            output_path,
            fps=24,
            codec='libx264',
            audio_codec='aac',
            preset='medium',
            ffmpeg_params=['-crf', '18'],  # High quality
            verbose=False,
            logger=None
        )
        
        # Cleanup
        final_video.close()
        for clip in video_clips:
            clip.close()
        
        # Remove temporary audio files
        for temp_file in temp_audio_files:
            try:
                os.remove(temp_file)
            except:
                pass
        
        print(f"🎉 Video created successfully: {output_path}")
        return output_path
        
    except Exception as e:
        print(f"❌ Error creating final video: {e}")
        
        # Cleanup on error
        for temp_file in temp_audio_files:
            try:
                os.remove(temp_file)
            except:
                pass
        
        return None

def resize_image_for_video(img, target_width=1920, target_height=1080):
    """
    Resize image for video while maintaining aspect ratio
    Centers the image on a black background
    """
    
    # Calculate scaling to fit within target dimensions
    img_ratio = img.width / img.height
    target_ratio = target_width / target_height
    
    if img_ratio > target_ratio:
        # Image is wider - scale by width
        new_width = target_width
        new_height = int(target_width / img_ratio)
    else:
        # Image is taller - scale by height
        new_height = target_height
        new_width = int(target_height * img_ratio)
    
    # Resize image
    img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
    
    # Create black background
    background = Image.new('RGB', (target_width, target_height), (0, 0, 0))
    
    # Center the image
    x_offset = (target_width - new_width) // 2
    y_offset = (target_height - new_height) // 2
    background.paste(img_resized, (x_offset, y_offset))
    
    return background

# Compatibility function for existing code
async def make_movie(movie_script, output_dir, video_name, narration_client):
    """
    Compatibility wrapper for the existing make_movie function
    """
    return await create_simple_manga_video(movie_script, output_dir, video_name)
