import asyncio
import os
import base64
import tempfile
import subprocess
from io import Bytes<PERSON>
from typing import List, Dict, Any
import edge_tts
from enhanced_vision_analysis import analyze_manga_segment_enhanced
from ultra_fast_video import create_ultra_fast_manga_clip

class UltraFastMovieDirector:
    """Ultra-fast movie director optimized for speed while keeping premium effects"""
    
    def __init__(self, voice: str = "en-US-AriaNeural"):
        self.voice = voice
        self.character_database = {}
        
    async def create_ultra_fast_movie(self, movie_script: List[Dict[str, Any]], 
                                    output_dir: str, movie_name: str, 
                                    volume_number: str = None) -> str:
        """Create movie with ultra-fast processing"""
        
        print("🚀 Creating Ultra-Fast Manga Recap with Premium Effects...")
        print(f"📊 Processing {len(movie_script)} segments")
        
        # Step 1: Enhanced analysis (parallel processing)
        print("\n🤖 Step 1: Ultra-Fast Character Analysis...")
        enhanced_script = await self._ultra_fast_analysis(movie_script)
        
        # Step 2: Generate audio (parallel processing)
        print("\n🗣️ Step 2: Ultra-Fast Audio Generation...")
        await self._ultra_fast_audio_generation(enhanced_script, output_dir)
        
        # Step 3: Create videos (parallel processing with OpenCV)
        print("\n🎨 Step 3: Ultra-Fast Video Creation...")
        video_files = await self._ultra_fast_video_creation(enhanced_script, output_dir)
        
        # Step 4: Ultra-fast final assembly with FFmpeg
        print("\n⚡ Step 4: Ultra-Fast Final Assembly...")
        final_video_path = await self._ultra_fast_final_assembly(
            video_files, enhanced_script, output_dir, movie_name, volume_number
        )
        
        return final_video_path
    
    async def _ultra_fast_analysis(self, movie_script: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Ultra-fast parallel analysis"""
        enhanced_script = []
        
        # Process multiple segments in parallel (limit to avoid overwhelming LM Studio)
        semaphore = asyncio.Semaphore(3)  # Max 3 concurrent requests
        
        async def analyze_segment(i, segment):
            async with semaphore:
                base64_images = segment.get('base64_images', [])
                
                if not base64_images:
                    return None
                
                try:
                    # Use faster analysis with fewer images
                    sample_images = base64_images[::max(1, len(base64_images)//3)]  # Sample every 3rd image
                    
                    enhanced_analysis = await analyze_manga_segment_enhanced(
                        sample_images, i
                    )
                    
                    enhanced_segment = {
                        'segment_index': i,
                        'base64_images': base64_images,  # Keep all images for video
                        'enhanced_narration': enhanced_analysis['narration'],
                        'characters': enhanced_analysis['characters'],
                        'scene_summary': enhanced_analysis['scene_summary'],
                        'total_pages': len(base64_images),
                        'audio_path': None
                    }
                    
                    print(f"   ✅ Analyzed segment {i+1}")
                    return enhanced_segment
                    
                except Exception as e:
                    print(f"   ⚠️ Error analyzing segment {i+1}: {e}")
                    # Fallback
                    return {
                        'segment_index': i,
                        'base64_images': base64_images,
                        'enhanced_narration': f"In this manga scene, the story continues with intense action and character development.",
                        'characters': [],
                        'scene_summary': 'Action continues.',
                        'total_pages': len(base64_images),
                        'audio_path': None
                    }
        
        # Process all segments in parallel
        tasks = [analyze_segment(i, segment) for i, segment in enumerate(movie_script)]
        results = await asyncio.gather(*tasks)
        
        enhanced_script = [r for r in results if r is not None]
        print(f"✅ Ultra-fast analysis completed: {len(enhanced_script)} segments")
        return enhanced_script
    
    async def _ultra_fast_audio_generation(self, enhanced_script: List[Dict[str, Any]], output_dir: str):
        """Ultra-fast parallel audio generation"""
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate audio for all segments in parallel
        semaphore = asyncio.Semaphore(5)  # More concurrent audio generation
        
        async def generate_audio(segment):
            async with semaphore:
                try:
                    segment_index = segment['segment_index']
                    narration_text = segment['enhanced_narration']
                    
                    # Add natural pauses for better pacing
                    narration_text = self._add_natural_pauses(narration_text)
                    
                    # Generate audio
                    communicate = edge_tts.Communicate(narration_text, self.voice)
                    
                    # Save directly to file (faster than BytesIO)
                    audio_path = f"{output_dir}/audio_{segment_index}.mp3"
                    await communicate.save(audio_path)
                    
                    segment['audio_path'] = audio_path
                    
                    # Get file size for progress
                    audio_size = os.path.getsize(audio_path)
                    print(f"   ✅ Audio {segment_index+1}: {audio_size} bytes")
                    
                except Exception as e:
                    print(f"   ❌ Error generating audio for segment {segment['segment_index']+1}: {e}")
                    segment['audio_path'] = None
        
        # Generate all audio in parallel
        await asyncio.gather(*[generate_audio(segment) for segment in enhanced_script])
        
        print(f"✅ Ultra-fast audio generation completed")
    
    def _add_natural_pauses(self, text: str) -> str:
        """Add natural pauses for better pacing"""
        text = text.replace('. ', '. <break time="0.3s"/> ')
        text = text.replace('! ', '! <break time="0.3s"/> ')
        text = text.replace('? ', '? <break time="0.3s"/> ')
        text = text.replace(', ', ', <break time="0.2s"/> ')
        return text
    
    async def _ultra_fast_video_creation(self, enhanced_script: List[Dict[str, Any]], 
                                       output_dir: str) -> List[str]:
        """Ultra-fast parallel video creation using OpenCV"""
        
        video_files = []
        
        # Process videos in parallel (limit to avoid overwhelming system)
        semaphore = asyncio.Semaphore(2)  # Max 2 concurrent video processing
        
        async def create_video(segment):
            async with semaphore:
                segment_index = segment['segment_index']
                base64_images = segment['base64_images']
                audio_path = segment['audio_path']
                
                if not audio_path or not os.path.exists(audio_path):
                    print(f"   ❌ No audio for segment {segment_index+1}")
                    return None
                
                # Output path for this segment
                video_path = f"{output_dir}/video_{segment_index}.mp4"
                
                print(f"   🚀 Creating ultra-fast video {segment_index+1}...")
                
                # Use ultra-fast video creation
                success = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    create_ultra_fast_manga_clip,
                    base64_images, audio_path, video_path, segment_index
                )
                
                if success and os.path.exists(video_path):
                    print(f"   ✅ Video {segment_index+1} created")
                    return video_path
                else:
                    print(f"   ❌ Failed to create video {segment_index+1}")
                    return None
        
        # Create all videos in parallel
        tasks = [create_video(segment) for segment in enhanced_script]
        results = await asyncio.gather(*tasks)
        
        video_files = [v for v in results if v is not None]
        print(f"✅ Ultra-fast video creation completed: {len(video_files)} videos")
        return video_files
    
    async def _ultra_fast_final_assembly(self, video_files: List[str], 
                                       enhanced_script: List[Dict[str, Any]],
                                       output_dir: str, movie_name: str, 
                                       volume_number: str = None) -> str:
        """Ultra-fast final assembly using FFmpeg concat"""
        
        if not video_files:
            raise Exception("No video files to assemble")
        
        # Sort video files by segment index
        video_files.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
        
        # Create concat file for FFmpeg
        concat_file = f"{output_dir}/concat_list.txt"
        with open(concat_file, 'w') as f:
            for video_file in video_files:
                f.write(f"file '{os.path.abspath(video_file)}'\n")
        
        # Determine output path
        if volume_number:
            final_path = f"{output_dir}/v{volume_number}/recap.mp4"
            os.makedirs(f"{output_dir}/v{volume_number}", exist_ok=True)
        else:
            final_path = f"{output_dir}/recap.mp4"
        
        print(f"   ⚡ Ultra-fast concatenation of {len(video_files)} videos...")
        
        # Use FFmpeg for ultra-fast concatenation
        ffmpeg_cmd = [
            'ffmpeg', '-y',  # Overwrite output
            '-f', 'concat',  # Concat demuxer
            '-safe', '0',  # Allow absolute paths
            '-i', concat_file,  # Input concat file
            '-c', 'copy',  # Copy streams (no re-encoding = ultra-fast)
            '-avoid_negative_ts', 'make_zero',
            final_path
        ]
        
        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=300)
            )
            
            if result.returncode == 0:
                print(f"✅ Ultra-fast final video created: {final_path}")
                
                # Cleanup temporary files
                self._cleanup_temp_files(output_dir, video_files, concat_file)
                
                return final_path
            else:
                print(f"❌ FFmpeg concatenation error: {result.stderr}")
                raise Exception(f"FFmpeg failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error in final assembly: {e}")
            raise
    
    def _cleanup_temp_files(self, output_dir: str, video_files: List[str], concat_file: str):
        """Clean up temporary files"""
        try:
            # Remove individual video files
            for video_file in video_files:
                os.remove(video_file)
            
            # Remove audio files
            import glob
            audio_files = glob.glob(f"{output_dir}/audio_*.mp3")
            for audio_file in audio_files:
                os.remove(audio_file)
            
            # Remove concat file
            os.remove(concat_file)
            
            print("🧹 Cleaned up temporary files")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")

# Main function for ultra-fast movie creation
async def create_ultra_fast_manga_movie(movie_script: List[Dict[str, Any]], 
                                      output_dir: str, movie_name: str, 
                                      volume_number: str = None) -> str:
    """Create ultra-fast manga movie with premium effects"""
    
    director = UltraFastMovieDirector()
    return await director.create_ultra_fast_movie(
        movie_script, output_dir, movie_name, volume_number
    )
