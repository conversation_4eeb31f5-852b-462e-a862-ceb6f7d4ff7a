#!/usr/bin/env python3
"""
Simplified Manga Recap System
Takes PDF → Extracts Pages → Generates Story → Creates Video
"""

import os
import asyncio
import argparse
from pathlib import Path

# Import existing modules we'll reuse
from manga_extraction import extract_all_pages_as_images
from vision_analysis import analyze_images_with_gpt4_vision
from prompts import BASIC_PROMPT, BASIC_INSTRUCTIONS

# Try to import movie creation functions
try:
    from simple_movie_maker import create_simple_manga_video
    MOVIE_MAKER_AVAILABLE = True
except ImportError:
    print("⚠️  Simple movie maker not available, trying original...")
    try:
        from movie_director import make_movie
        MOVIE_MAKER_AVAILABLE = "original"
    except ImportError:
        print("❌ No movie creation modules available")
        MOVIE_MAKER_AVAILABLE = False

async def create_simple_manga_recap(pdf_path, output_name):
    """
    Simplified manga recap creation process:
    1. Extract all pages from PDF
    2. Analyze pages with vision model to create story
    3. Generate narration and video
    """
    
    print("🎬 Starting Simplified Manga Recap Creation")
    print(f"📖 Processing: {pdf_path}")
    print(f"🎯 Output: {output_name}")
    
    # Step 1: Extract all pages from PDF
    print("\n📄 Step 1: Extracting pages from PDF...")
    try:
        pages_data = extract_all_pages_as_images(pdf_path)
        volume = pages_data["scaled"]  # Use scaled images for analysis
        volume_unscaled = pages_data["full"]  # Use full resolution for video
        
        print(f"✅ Extracted {len(volume)} pages successfully")
        
        if len(volume) == 0:
            print("❌ No pages found in PDF")
            return False
            
    except Exception as e:
        print(f"❌ Error extracting pages: {e}")
        return False
    
    # Step 2: Split pages into manageable segments for analysis
    print("\n🔍 Step 2: Creating story segments...")
    
    # Simple segmentation: every 10-15 pages
    pages_per_segment = 12
    segments = []
    
    for i in range(0, len(volume), pages_per_segment):
        end_idx = min(i + pages_per_segment, len(volume))
        segment_pages = volume[i:end_idx]
        segment_pages_unscaled = volume_unscaled[i:end_idx]
        
        segments.append({
            'pages': segment_pages,
            'pages_unscaled': segment_pages_unscaled,
            'start_page': i,
            'end_page': end_idx - 1
        })
    
    print(f"✅ Created {len(segments)} segments")
    
    # Step 3: Analyze each segment with vision model
    print("\n🤖 Step 3: Analyzing content with vision model...")
    
    movie_script = []
    
    for idx, segment in enumerate(segments):
        print(f"   Analyzing segment {idx + 1}/{len(segments)} (pages {segment['start_page']}-{segment['end_page']})...")
        
        try:
            # Use existing vision analysis function
            response = analyze_images_with_gpt4_vision(
                character_profiles=[],  # No character profiles needed
                pages=segment['pages'],
                client=None,  # Not used with LM Studio
                prompt=BASIC_PROMPT,
                instructions=BASIC_INSTRUCTIONS
            )
            
            story_text = response.choices[0].message.content
            
            # Create movie script segment with proper structure for existing movie_director
            movie_script.append({
                'text': story_text,
                'images': segment['pages_unscaled'],  # Use unscaled for better quality
                'images_unscaled': segment['pages_unscaled'],
                'segment_id': idx,
                'important_panels': list(range(len(segment['pages_unscaled'])))  # Use all images as panels
            })
            
            print(f"   ✅ Generated story for segment {idx + 1}")
            
        except Exception as e:
            print(f"   ❌ Error analyzing segment {idx + 1}: {e}")
            # Continue with other segments
            continue
    
    if not movie_script:
        print("❌ No story content generated")
        return False
    
    print(f"✅ Generated story content for {len(movie_script)} segments")
    
    # Step 4: Create video recap
    print("\n🎬 Step 4: Creating video recap...")
    
    try:
        # Create output directory
        output_dir = Path(output_name).parent
        output_dir.mkdir(parents=True, exist_ok=True)

        if not MOVIE_MAKER_AVAILABLE:
            print("❌ No movie creation modules available")
            return False

        # Use available movie creation function
        if MOVIE_MAKER_AVAILABLE == True:
            video_path = await create_simple_manga_video(movie_script, str(output_dir), Path(output_name).stem)
        else:
            # Use original movie_director with our adapted structure
            from movie_director import make_movie
            await make_movie(movie_script, str(output_dir), Path(output_name).stem, None)
            video_path = output_dir / f"{Path(output_name).stem}.mp4"
            video_path = str(video_path) if video_path.exists() else None

        if video_path:
            print(f"🎉 Video created successfully: {video_path}")
            print(f"💰 Total cost: $0.00 (FREE with LM Studio + Edge-TTS!)")
            return video_path
        else:
            print("❌ Video creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error creating video: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Create manga recap video from PDF')
    parser.add_argument('pdf_path', help='Path to the manga PDF file')
    parser.add_argument('--output', '-o', default='output/manga_recap', 
                       help='Output name (without extension)')
    
    args = parser.parse_args()
    
    # Validate input
    if not os.path.exists(args.pdf_path):
        print(f"❌ PDF file not found: {args.pdf_path}")
        return
    
    if not args.pdf_path.lower().endswith('.pdf'):
        print(f"❌ File must be a PDF: {args.pdf_path}")
        return
    
    # Run the recap creation
    result = asyncio.run(create_simple_manga_recap(args.pdf_path, args.output))
    
    if result:
        print(f"\n🎉 SUCCESS! Manga recap created: {result}")
    else:
        print(f"\n❌ FAILED! Could not create manga recap")

if __name__ == "__main__":
    print("🎬 Simplified Manga Recap System")
    print("🤖 Make sure LM Studio is running on http://localhost:1234")
    print("🗣️ Using Edge-TTS for free narration")
    print("💰 100% FREE - No API costs!\n")
    
    main()
