#pragma once

// @generated by torchgen/gen.py from NativeFunction.h

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>


namespace at {
namespace native {
TORCH_API at::Tensor unsqueeze(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor unsqueeze_nested(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor unsqueeze_sparse(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor unsqueeze_quantized(const at::Tensor & self, int64_t dim);
TORCH_API at::Tensor & unsqueeze_(at::Tensor & self, int64_t dim);
} // namespace native
} // namespace at
