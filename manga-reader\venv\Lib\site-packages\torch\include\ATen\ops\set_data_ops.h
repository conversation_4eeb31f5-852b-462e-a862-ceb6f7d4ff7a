#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API set_data {
  using schema = void (at::Tensor &, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::set_data";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "set_data(Tensor(a!) self, Tensor new_data) -> ()";
  static void call(at::Tensor & self, const at::Tensor & new_data);
  static void redispatch(c10::DispatchKeySet dispatchKeySet, at::Tensor & self, const at::Tensor & new_data);
};

}} // namespace at::_ops
