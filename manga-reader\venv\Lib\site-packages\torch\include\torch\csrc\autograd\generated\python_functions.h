#pragma once

#include <Python.h>

// @generated from ..\tools\autograd\templates/python_functions.h

// Python bindings for automatically generated autograd functions

namespace torch { namespace autograd { namespace generated {

void initialize_autogenerated_functions_0(PyObject* module);
void initialize_autogenerated_functions_1(PyObject* module);
void initialize_autogenerated_functions_2(PyObject* module);
void initialize_autogenerated_functions_3(PyObject* module);
void initialize_autogenerated_functions_4(PyObject* module);

inline void initialize_autogenerated_functions(PyObject* module) {
  initialize_autogenerated_functions_0(module);
  initialize_autogenerated_functions_1(module);
  initialize_autogenerated_functions_2(module);
  initialize_autogenerated_functions_3(module);
  initialize_autogenerated_functions_4(module);
}

}}} // namespace torch::autograd::generated
