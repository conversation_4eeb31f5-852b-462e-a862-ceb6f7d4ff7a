import numpy as np
from PIL import Image, ImageFilter, ImageEnhance
from io import BytesIO
import base64
import cv2
import os
from typing import List
import subprocess
import tempfile

class UltraFastVideoEffects:
    """Ultra-fast video effects using OpenCV and FFmpeg for maximum speed"""
    
    def __init__(self, target_width=1920, target_height=1080, fps=24):
        self.target_width = target_width
        self.target_height = target_height
        self.fps = fps
        self.aspect_ratio = target_width / target_height
    
    def create_blurred_background_fast(self, image_data: bytes, blur_radius: int = 15) -> np.ndarray:
        """Create blurred background using OpenCV for speed"""
        # Decode image directly to numpy array
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # Resize to target resolution using OpenCV (much faster than PIL)
        background = cv2.resize(img, (self.target_width, self.target_height), interpolation=cv2.INTER_LINEAR)
        
        # Apply Gaussian blur (OpenCV is much faster than PIL)
        background = cv2.GaussianBlur(background, (blur_radius*2+1, blur_radius*2+1), 0)
        
        # Darken background
        background = (background * 0.6).astype(np.uint8)
        
        return background
    
    def create_foreground_fast(self, image_data: bytes, max_scale: float = 0.8) -> tuple:
        """Create foreground image using OpenCV for speed"""
        # Decode image
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        h, w = img.shape[:2]
        img_aspect = w / h
        
        # Calculate target size
        if img_aspect > self.aspect_ratio:
            target_width = int(self.target_width * max_scale)
            target_height = int(target_width / img_aspect)
        else:
            target_height = int(self.target_height * max_scale)
            target_width = int(target_height * img_aspect)
        
        # Resize using OpenCV
        foreground = cv2.resize(img, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
        
        # Calculate position for centering
        x = (self.target_width - target_width) // 2
        y = (self.target_height - target_height) // 2
        
        return foreground, x, y
    
    def create_composite_frame_fast(self, image_data: bytes) -> np.ndarray:
        """Create composite frame using OpenCV for maximum speed"""
        # Create blurred background
        background = self.create_blurred_background_fast(image_data)
        
        # Create foreground
        foreground, x, y = self.create_foreground_fast(image_data)
        
        # Composite using OpenCV (much faster than PIL)
        h, w = foreground.shape[:2]
        background[y:y+h, x:x+w] = foreground
        
        return background
    
    def create_ultra_fast_video(self, base64_images: List[str], audio_path: str, 
                               output_path: str, segment_index: int = 0) -> bool:
        """Create video using OpenCV + FFmpeg for ultra-fast processing"""
        
        if not base64_images:
            return False
        
        print(f"   🚀 Ultra-fast processing segment {segment_index}")
        
        # Get audio duration using FFprobe (faster than loading audio)
        try:
            result = subprocess.run([
                'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
                '-of', 'default=noprint_wrappers=1:nokey=1', audio_path
            ], capture_output=True, text=True, timeout=10)
            audio_duration = float(result.stdout.strip())
        except:
            # Fallback: estimate from file size
            audio_size = os.path.getsize(audio_path)
            audio_duration = audio_size / 32000  # Rough estimation
        
        print(f"   Audio duration: {audio_duration:.2f}s")
        
        # Calculate optimal frame distribution
        total_frames = int(audio_duration * self.fps)
        frames_per_image = max(1, total_frames // len(base64_images))
        
        print(f"   Creating {total_frames} frames ({frames_per_image} per image)")
        
        # Create temporary video file
        temp_video = tempfile.NamedTemporaryFile(suffix='.mp4', delete=False)
        temp_video.close()
        
        # Use OpenCV VideoWriter for ultra-fast frame writing
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(temp_video.name, fourcc, self.fps, 
                             (self.target_width, self.target_height))
        
        if not out.isOpened():
            print(f"   ❌ Failed to open video writer")
            return False
        
        try:
            frame_count = 0
            
            for i, img_b64 in enumerate(base64_images):
                try:
                    # Decode image
                    image_data = base64.b64decode(img_b64)
                    
                    # Create composite frame with effects
                    frame = self.create_composite_frame_fast(image_data)
                    
                    # Write frames for this image
                    frames_for_this_image = frames_per_image
                    if i == len(base64_images) - 1:
                        # Last image gets remaining frames
                        frames_for_this_image = total_frames - frame_count
                    
                    for _ in range(frames_for_this_image):
                        out.write(frame)
                        frame_count += 1
                        
                        if frame_count % 100 == 0:
                            print(f"   📹 Written {frame_count}/{total_frames} frames", end='\r')
                    
                except Exception as e:
                    print(f"   ⚠️ Error processing image {i}: {e}")
                    continue
            
            out.release()
            print(f"\n   ✅ Video frames written: {frame_count}")
            
            # Use FFmpeg to combine video with audio (ultra-fast)
            print(f"   🎵 Combining with audio...")
            
            ffmpeg_cmd = [
                'ffmpeg', '-y',  # Overwrite output
                '-i', temp_video.name,  # Video input
                '-i', audio_path,  # Audio input
                '-c:v', 'libx264',  # Video codec
                '-preset', 'ultrafast',  # Fastest encoding preset
                '-crf', '28',  # Good quality/speed balance
                '-c:a', 'aac',  # Audio codec
                '-shortest',  # Match shortest stream
                '-avoid_negative_ts', 'make_zero',
                output_path
            ]
            
            result = subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"   ✅ Ultra-fast video created: {output_path}")
                # Cleanup
                os.unlink(temp_video.name)
                return True
            else:
                print(f"   ❌ FFmpeg error: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"   ❌ Error in video creation: {e}")
            out.release()
            return False
        
        finally:
            # Cleanup temp file
            try:
                os.unlink(temp_video.name)
            except:
                pass

def create_ultra_fast_manga_clip(base64_images: List[str], audio_path: str,
                                output_path: str, segment_index: int = 0, use_groups: bool = False) -> bool:
    """Create ultra-fast manga video clip"""
    effects = UltraFastVideoEffects()

    if use_groups:
        # Group images into sets of 3 and combine them horizontally
        grouped_images = []
        for i in range(0, len(base64_images), 3):
            group = base64_images[i:i+3]
            # If we have less than 3 images, pad with the last image
            while len(group) < 3 and len(group) > 0:
                group.append(group[-1])
            if len(group) > 0:
                combined_image = combine_images_horizontally_b64(group)
                grouped_images.append(combined_image)

        return effects.create_ultra_fast_video(
            grouped_images, audio_path, output_path, segment_index
        )
    else:
        return effects.create_ultra_fast_video(
            base64_images, audio_path, output_path, segment_index
        )

def combine_images_horizontally_b64(base64_images: List[str]) -> str:
    """Combine base64 images horizontally into one image"""
    from PIL import Image
    import io
    import base64

    # Decode base64 images
    pil_images = []
    for b64_img in base64_images:
        img_data = base64.b64decode(b64_img)
        pil_img = Image.open(io.BytesIO(img_data))
        pil_images.append(pil_img)

    # Get dimensions (assume all images have same height)
    width, height = pil_images[0].size

    # Create combined image (3 times the width)
    combined_width = width * len(pil_images)
    combined_image = Image.new('RGB', (combined_width, height), 'white')

    # Paste images side by side
    for i, img in enumerate(pil_images):
        combined_image.paste(img, (i * width, 0))

    # Convert back to base64
    buffer = io.BytesIO()
    combined_image.save(buffer, format='PNG')
    combined_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

    return combined_b64
