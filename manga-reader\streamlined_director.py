import asyncio
import os
import tempfile
import subprocess
import re
from typing import List, Dict, Any
import edge_tts
from simple_text_processor import SimpleTextProcessor
from ultra_fast_video import create_ultra_fast_manga_clip

class StreamlinedDirector:
    """Streamlined director for manual narrations - no AI analysis"""

    def __init__(self, voice: str = "en-US-BrianNeural"):
        self.voice = voice
        self.text_processor = SimpleTextProcessor()
        
    async def create_streamlined_movie(self, base64_images: List[str], 
                                     manual_narrations: str,
                                     output_dir: str, movie_name: str, 
                                     volume_number: str = None) -> str:
        """Create movie with manual narrations - no AI processing"""
        
        print("🎯 Creating Streamlined Manga Recap with Manual Narrations...")
        print(f"📊 Processing {len(base64_images)} pages")
        
        # Step 1: Parse manual narrations
        print("\n📝 Step 1: Processing Manual Narrations...")
        narrations = await self._process_manual_narrations(manual_narrations, len(base64_images))
        
        # Step 2: Generate audio for each page
        print("\n🗣️ Step 2: Ultra-Fast Audio Generation...")
        audio_files = await self._generate_page_audio(narrations, output_dir)
        
        # Step 3: Create video for each page
        print("\n🎨 Step 3: Ultra-Fast Video Creation...")
        video_files = await self._create_page_videos(base64_images, audio_files, output_dir)
        
        # Step 4: Final assembly
        print("\n⚡ Step 4: Ultra-Fast Final Assembly...")
        final_video_path = await self._assemble_final_video(
            video_files, output_dir, movie_name, volume_number
        )
        
        return final_video_path
    
    async def _process_manual_narrations(self, manual_narrations: str, total_pages: int) -> Dict[int, str]:
        """Process and validate manual narrations"""
        
        print(f"   📝 Parsing manual narrations...")
        
        # Parse the narrations
        narrations = self.text_processor.parse_manual_narrations(manual_narrations)
        
        # Validate narrations
        is_valid, errors = self.text_processor.validate_narrations(narrations, total_pages)
        
        if not is_valid:
            print(f"   ⚠️ Narration validation errors:")
            for error in errors:
                print(f"      - {error}")
            
            # Fill missing pages with placeholder narrations
            for page_num in range(1, total_pages + 1):
                if page_num not in narrations:
                    narrations[page_num] = f"Page {page_num} continues the story."
                    print(f"   🔧 Added placeholder for page {page_num}")
        
        # Get statistics
        stats = self.text_processor.get_narration_stats(narrations)
        print(f"   ✅ Processed {stats['total_pages']} narrations")
        print(f"   📊 Total words: {stats['total_words']}")
        print(f"   📊 Average words per page: {stats['avg_words_per_page']}")
        print(f"   📊 Page range: {stats['page_range'][0]}-{stats['page_range'][1]}")
        
        # Show sample narrations
        sample_pages = sorted(list(narrations.keys())[:3])
        for page_num in sample_pages:
            print(f"   📖 Page {page_num}: {narrations[page_num][:60]}...")
        
        return narrations
    
    async def _generate_page_audio(self, narrations: Dict[int, str], 
                                 output_dir: str) -> Dict[int, str]:
        """Generate audio for each page narration"""
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Generate audio for all pages in parallel
        semaphore = asyncio.Semaphore(5)
        
        async def generate_audio_for_page(page_num, narration):
            async with semaphore:
                try:
                    # Add natural pauses
                    enhanced_narration = self._add_natural_pauses(narration)
                    
                    # Generate audio
                    communicate = edge_tts.Communicate(enhanced_narration, self.voice)
                    
                    # Save to file
                    audio_path = f"{output_dir}/page_{page_num:03d}_audio.mp3"
                    await communicate.save(audio_path)
                    
                    # Get file size for progress
                    audio_size = os.path.getsize(audio_path)
                    print(f"   ✅ Page {page_num} audio: {audio_size} bytes")
                    
                    return page_num, audio_path
                    
                except Exception as e:
                    print(f"   ❌ Error generating audio for page {page_num}: {e}")
                    return page_num, None
        
        # Generate all audio in parallel
        tasks = [
            generate_audio_for_page(page_num, narration) 
            for page_num, narration in narrations.items()
        ]
        
        results = await asyncio.gather(*tasks)
        
        # Create audio files dictionary
        audio_files = {}
        for page_num, audio_path in results:
            if audio_path:
                audio_files[page_num] = audio_path
        
        print(f"✅ Generated audio for {len(audio_files)} pages")
        return audio_files
    
    def _add_natural_pauses(self, text: str) -> str:
        """Add natural pauses for better narration - clean approach"""
        # Clean the text first - remove any existing SSML or break tags
        text = re.sub(r'<[^>]+>', '', text)
        text = re.sub(r'break time.*?s/', '', text)
        text = re.sub(r'\s+', ' ', text).strip()

        # Add natural pauses using periods and commas (no SSML)
        text = text.replace('. ', '... ')
        text = text.replace('! ', '! ')
        text = text.replace('? ', '? ')
        text = text.replace(', ', ', ')
        text = text.replace(': ', ': ')

        return text
    
    async def _create_page_videos(self, base64_images: List[str], 
                                audio_files: Dict[int, str], 
                                output_dir: str) -> List[str]:
        """Create video for each page"""
        
        video_files = []
        
        # Process videos in parallel (limit to avoid overwhelming system)
        semaphore = asyncio.Semaphore(2)
        
        async def create_page_video(page_num, base64_image):
            async with semaphore:
                audio_path = audio_files.get(page_num)
                
                if not audio_path or not os.path.exists(audio_path):
                    print(f"   ❌ No audio for page {page_num}")
                    return None
                
                # Output path for this page video
                video_path = f"{output_dir}/page_{page_num:03d}_video.mp4"
                
                print(f"   🚀 Creating video for page {page_num}...")
                
                # Create video with single page
                success = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    create_ultra_fast_manga_clip,
                    [base64_image],  # Single page as list
                    audio_path, 
                    video_path, 
                    page_num - 1
                )
                
                if success and os.path.exists(video_path):
                    print(f"   ✅ Page {page_num} video created")
                    return video_path
                else:
                    print(f"   ❌ Failed to create video for page {page_num}")
                    return None
        
        # Create videos for all pages that have audio
        tasks = []
        for i, base64_image in enumerate(base64_images):
            page_num = i + 1
            if page_num in audio_files:
                tasks.append(create_page_video(page_num, base64_image))
        
        results = await asyncio.gather(*tasks)
        video_files = [v for v in results if v is not None]
        
        print(f"✅ Created {len(video_files)} page videos")
        return video_files
    
    async def _assemble_final_video(self, video_files: List[str], 
                                  output_dir: str, movie_name: str, 
                                  volume_number: str = None) -> str:
        """Assemble final video using FFmpeg"""
        
        if not video_files:
            raise Exception("No video files to assemble")
        
        # Sort video files by page number
        def extract_page_num(path):
            try:
                return int(path.split('page_')[1].split('_')[0])
            except:
                return 0
        
        video_files.sort(key=extract_page_num)
        
        # Create concat file for FFmpeg
        concat_file = f"{output_dir}/concat_list.txt"
        with open(concat_file, 'w') as f:
            for video_file in video_files:
                f.write(f"file '{os.path.abspath(video_file)}'\n")
        
        # Determine output path
        if volume_number:
            final_path = f"{output_dir}/v{volume_number}/recap.mp4"
            os.makedirs(f"{output_dir}/v{volume_number}", exist_ok=True)
        else:
            final_path = f"{output_dir}/recap.mp4"
        
        print(f"   ⚡ Ultra-fast concatenation of {len(video_files)} page videos...")
        
        # Use FFmpeg for ultra-fast concatenation
        ffmpeg_cmd = [
            'ffmpeg', '-y',  # Overwrite output
            '-f', 'concat',  # Concat demuxer
            '-safe', '0',  # Allow absolute paths
            '-i', concat_file,  # Input concat file
            '-c', 'copy',  # Copy streams (no re-encoding = ultra-fast)
            '-avoid_negative_ts', 'make_zero',
            final_path
        ]
        
        try:
            result = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: subprocess.run(ffmpeg_cmd, capture_output=True, text=True, timeout=300)
            )
            
            if result.returncode == 0:
                print(f"✅ Ultra-fast final video created: {final_path}")
                
                # Cleanup temporary files
                self._cleanup_temp_files(output_dir, video_files, concat_file)
                
                return final_path
            else:
                print(f"❌ FFmpeg concatenation error: {result.stderr}")
                raise Exception(f"FFmpeg failed: {result.stderr}")
                
        except Exception as e:
            print(f"❌ Error in final assembly: {e}")
            raise
    
    def _cleanup_temp_files(self, output_dir: str, video_files: List[str], concat_file: str):
        """Clean up temporary files"""
        try:
            # Remove individual video files
            for video_file in video_files:
                os.remove(video_file)
            
            # Remove audio files
            import glob
            audio_files = glob.glob(f"{output_dir}/page_*_audio.mp3")
            for audio_file in audio_files:
                os.remove(audio_file)
            
            # Remove concat file
            os.remove(concat_file)
            
            print("🧹 Cleaned up temporary files")
        except Exception as e:
            print(f"⚠️ Cleanup warning: {e}")

# Main function for streamlined movie creation
async def create_streamlined_manga_movie(base64_images: List[str],
                                       manual_narrations: str,
                                       output_dir: str, movie_name: str,
                                       volume_number: str = None,
                                       use_groups: bool = True) -> str:
    """Create streamlined manga movie with manual narrations"""

    if use_groups:
        # Group images into sets of 3 and combine them horizontally
        grouped_images = []
        for i in range(0, len(base64_images), 3):
            group = base64_images[i:i+3]
            # If we have less than 3 images, pad with the last image
            while len(group) < 3 and len(group) > 0:
                group.append(group[-1])
            if len(group) > 0:
                combined_image = combine_images_horizontally_b64(group)
                grouped_images.append(combined_image)

        director = StreamlinedDirector()
        return await director.create_streamlined_movie(
            grouped_images, manual_narrations, output_dir, movie_name, volume_number
        )
    else:
        director = StreamlinedDirector()
        return await director.create_manga_movie(
            base64_images, manual_narrations, output_dir, movie_name, volume_number
        )

def combine_images_horizontally_b64(base64_images: List[str]) -> str:
    """Combine base64 images horizontally into one image"""
    from PIL import Image
    import io
    import base64

    # Decode base64 images
    pil_images = []
    for b64_img in base64_images:
        img_data = base64.b64decode(b64_img)
        pil_img = Image.open(io.BytesIO(img_data))
        pil_images.append(pil_img)

    # Get dimensions (assume all images have same height)
    width, height = pil_images[0].size

    # Create combined image (3 times the width)
    combined_width = width * len(pil_images)
    combined_image = Image.new('RGB', (combined_width, height), 'white')

    # Paste images side by side
    for i, img in enumerate(pil_images):
        combined_image.paste(img, (i * width, 0))

    # Convert back to base64
    buffer = io.BytesIO()
    combined_image.save(buffer, format='PNG')
    combined_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

    return combined_b64
