#pragma once

// @generated by torchgen/gen.py from Operator.h

#include <string_view>
#include <tuple>
#include <vector>

// Forward declarations of any types needed in the operator signatures.
// We can't directly include these classes because it will cause circular include dependencies.
// This file is included by TensorBody.h, which defines the Tensor class.
#include <ATen/core/ATen_fwd.h>

namespace at {
namespace _ops {


struct TORCH_API resize_as_ {
  using schema = const at::Tensor & (const at::Tensor &, const at::Tensor &, ::std::optional<at::MemoryFormat>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::resize_as_";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "resize_as_(Tensor(a!) self, Tensor the_template, *, MemoryFormat? memory_format=None) -> Tensor(a!)";
  static const at::Tensor & call(const at::Tensor & self, const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format);
  static const at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format);
};

struct TORCH_API resize_as_out {
  using schema = const at::Tensor & (const at::Tensor &, const at::Tensor &, ::std::optional<at::MemoryFormat>, const at::Tensor &);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::resize_as";
  static constexpr const char* overload_name = "out";
  static constexpr const char* schema_str = "resize_as.out(Tensor self, Tensor the_template, *, MemoryFormat? memory_format=None, Tensor(a!) out) -> Tensor(a!)";
  static const at::Tensor & call(const at::Tensor & self, const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format, const at::Tensor & out);
  static const at::Tensor & redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format, const at::Tensor & out);
};

struct TORCH_API resize_as {
  using schema = at::Tensor (const at::Tensor &, const at::Tensor &, ::std::optional<at::MemoryFormat>);
  using ptr_schema = schema*;
  // See Note [static constexpr char* members for windows NVCC]
  static constexpr const char* name = "aten::resize_as";
  static constexpr const char* overload_name = "";
  static constexpr const char* schema_str = "resize_as(Tensor self, Tensor the_template, *, MemoryFormat? memory_format=None) -> Tensor";
  static at::Tensor call(const at::Tensor & self, const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format);
  static at::Tensor redispatch(c10::DispatchKeySet dispatchKeySet, const at::Tensor & self, const at::Tensor & the_template, ::std::optional<at::MemoryFormat> memory_format);
};

}} // namespace at::_ops
