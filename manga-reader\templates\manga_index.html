<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎬 Manga Recap Creator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 100%;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .upload-section {
            margin-bottom: 30px;
        }

        .upload-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.3em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-upload {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .file-upload:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .file-upload.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .file-upload input[type="file"] {
            display: none;
        }

        .file-upload-text {
            color: #667eea;
            font-size: 1.1em;
            margin-bottom: 10px;
        }

        .file-upload-hint {
            color: #999;
            font-size: 0.9em;
        }

        .narration-section {
            margin-bottom: 30px;
        }

        .narration-options {
            margin-bottom: 20px;
            text-align: center;
        }

        .generate-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .generate-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(40, 167, 69, 0.3);
        }

        .generate-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .or-divider {
            position: relative;
            margin: 20px 0;
            text-align: center;
        }

        .or-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }

        .or-divider span {
            background: white;
            padding: 0 20px;
            color: #999;
            font-weight: 600;
        }

        .narration-textarea {
            width: 100%;
            min-height: 300px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        .narration-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .example-format {
            background: #f8f9ff;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 0 10px 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            color: #555;
        }

        .submit-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 20px;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .submit-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .progress-container {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
            border: 2px solid #667eea;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 10px;
        }

        .progress-text {
            text-align: center;
            color: #333;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .progress-stage {
            text-align: center;
            color: #666;
            font-size: 0.9em;
        }

        .download-section {
            display: none;
            margin-top: 30px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 15px;
            border: 2px solid #4caf50;
            text-align: center;
        }

        .download-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 50px;
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .download-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(76, 175, 80, 0.3);
        }

        .error-message {
            display: none;
            margin-top: 20px;
            padding: 15px;
            background: #ffe6e6;
            border: 2px solid #ff4444;
            border-radius: 10px;
            color: #cc0000;
            text-align: center;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #f0f0f0;
        }

        .feature {
            text-align: center;
            padding: 20px;
            background: #f8f9ff;
            border-radius: 15px;
        }

        .feature-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }

        .feature h4 {
            color: #333;
            margin-bottom: 5px;
        }

        .feature p {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎬 Manga Recap Creator</h1>
            <p>Transform your manga PDFs into professional recap videos with custom narrations</p>
        </div>

        <form id="uploadForm" enctype="multipart/form-data">
            <div class="upload-section">
                <h3>📖 Upload Your Manga PDF</h3>
                <div class="file-upload" onclick="document.getElementById('pdfFile').click()">
                    <input type="file" id="pdfFile" name="pdf_file" accept=".pdf" required>
                    <div class="file-upload-text">
                        <strong>Click to select your manga PDF</strong>
                    </div>
                    <div class="file-upload-hint">
                        Or drag and drop your PDF file here (Max: 500MB)
                    </div>
                </div>
                <div id="fileName" style="text-align: center; color: #667eea; font-weight: 600;"></div>
            </div>

            <div class="narration-section">
                <h3>📝 Enter Your Narrations</h3>
                <div class="narration-options">
                    <button type="button" class="generate-btn" id="generateBtn">
                        🤖 Auto-Generate Narrations with AI
                    </button>
                    <div class="or-divider">
                        <span>OR</span>
                    </div>
                </div>
                <div class="example-format">
                    <strong>Format Example:</strong><br>
                    Page 1<br>
                    Your narration for page 1 goes here.<br><br>
                    Page 2<br>
                    Your narration for page 2 goes here.<br><br>
                    Page 3<br>
                    Continue this pattern for all pages...
                </div>
                <textarea
                    id="narrationsText"
                    name="narrations_text"
                    class="narration-textarea"
                    placeholder="Page 1&#10;The story begins with our protagonist in a mysterious setting.&#10;&#10;Page 2&#10;The character explores their surroundings, taking in the new environment.&#10;&#10;Page 3&#10;A sense of wonder and curiosity fills the air as the journey continues.&#10;&#10;(Continue this pattern for all pages...)"
                    required></textarea>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                🚀 Create Manga Recap Video
            </button>
        </form>

        <div class="progress-container" id="progressContainer">
            <div class="progress-text" id="progressText">Processing...</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-stage" id="progressStage">Preparing...</div>
        </div>

        <div class="download-section" id="downloadSection">
            <h3>✅ Video Created Successfully!</h3>
            <p>Your manga recap video is ready for download.</p>
            <a href="#" class="download-btn" id="downloadBtn">📥 Download Video</a>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <div class="features">
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <h4>Ultra-Fast</h4>
                <p>Professional videos in minutes, not hours</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <h4>Premium Effects</h4>
                <p>Blurred backgrounds and perfect sync</p>
            </div>
            <div class="feature">
                <div class="feature-icon">🎯</div>
                <h4>Full Control</h4>
                <p>Custom narrations for every page</p>
            </div>
            <div class="feature">
                <div class="feature-icon">💰</div>
                <h4>100% Free</h4>
                <p>No subscriptions or hidden costs</p>
            </div>
        </div>
    </div>

    <script>
        let currentJobId = null;
        let statusInterval = null;

        // File upload handling
        const fileUpload = document.querySelector('.file-upload');
        const fileInput = document.getElementById('pdfFile');
        const fileName = document.getElementById('fileName');
        const generateBtn = document.getElementById('generateBtn');
        const narrationsText = document.getElementById('narrationsText');

        fileInput.addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                fileName.textContent = `Selected: ${e.target.files[0].name}`;
            }
        });

        // Drag and drop
        fileUpload.addEventListener('dragover', function(e) {
            e.preventDefault();
            fileUpload.classList.add('dragover');
        });

        fileUpload.addEventListener('dragleave', function(e) {
            e.preventDefault();
            fileUpload.classList.remove('dragover');
        });

        fileUpload.addEventListener('drop', function(e) {
            e.preventDefault();
            fileUpload.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                fileName.textContent = `Selected: ${files[0].name}`;
            }
        });

        // Generate narrations button
        generateBtn.addEventListener('click', async function() {
            if (!fileInput.files.length) {
                showError('Please select a PDF file first');
                return;
            }

            const formData = new FormData();
            formData.append('pdf_file', fileInput.files[0]);

            generateBtn.disabled = true;
            generateBtn.textContent = '🔄 Generating Narrations...';

            const progressContainer = document.getElementById('progressContainer');
            const errorMessage = document.getElementById('errorMessage');

            progressContainer.style.display = 'block';
            errorMessage.style.display = 'none';

            try {
                const response = await fetch('/generate_narrations', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    currentJobId = result.job_id;
                    startNarrationPolling();
                } else {
                    throw new Error(result.error || 'Narration generation failed');
                }

            } catch (error) {
                showError(error.message);
                resetGenerateButton();
                progressContainer.style.display = 'none';
            }
        });

        function startNarrationPolling() {
            statusInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/status/${currentJobId}`);
                    const status = await response.json();

                    updateProgress(status);

                    if (status.status === 'narrations_completed') {
                        clearInterval(statusInterval);
                        await loadGeneratedNarrations();
                    } else if (status.status === 'error') {
                        clearInterval(statusInterval);
                        showError(status.stage);
                        resetGenerateButton();
                        document.getElementById('progressContainer').style.display = 'none';
                    }

                } catch (error) {
                    clearInterval(statusInterval);
                    showError('Failed to get narration status');
                    resetGenerateButton();
                    document.getElementById('progressContainer').style.display = 'none';
                }
            }, 2000);
        }

        async function loadGeneratedNarrations() {
            try {
                const response = await fetch(`/get_narrations/${currentJobId}`);
                const result = await response.json();

                if (result.success) {
                    narrationsText.value = result.narrations_text;
                    showSuccess(`Generated narrations for ${result.total_pages} pages!`);
                } else {
                    throw new Error(result.error || 'Failed to load narrations');
                }

            } catch (error) {
                showError(error.message);
            }

            resetGenerateButton();
            document.getElementById('progressContainer').style.display = 'none';
        }

        function resetGenerateButton() {
            generateBtn.disabled = false;
            generateBtn.textContent = '🤖 Auto-Generate Narrations with AI';
        }

        function showSuccess(message) {
            const progressStage = document.getElementById('progressStage');
            progressStage.textContent = message;
            progressStage.style.color = '#28a745';
            progressStage.style.fontWeight = '600';
        }

        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = document.getElementById('submitBtn');
            const progressContainer = document.getElementById('progressContainer');
            const errorMessage = document.getElementById('errorMessage');

            // Reset UI
            submitBtn.disabled = true;
            submitBtn.textContent = '🔄 Processing...';
            progressContainer.style.display = 'block';
            errorMessage.style.display = 'none';
            document.getElementById('downloadSection').style.display = 'none';

            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    currentJobId = result.job_id;
                    startStatusPolling();
                } else {
                    throw new Error(result.error || 'Upload failed');
                }

            } catch (error) {
                showError(error.message);
                resetForm();
            }
        });

        function startStatusPolling() {
            statusInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/status/${currentJobId}`);
                    const status = await response.json();

                    updateProgress(status);

                    if (status.status === 'completed') {
                        clearInterval(statusInterval);
                        showDownload();
                    } else if (status.status === 'error') {
                        clearInterval(statusInterval);
                        showError(status.stage);
                        resetForm();
                    }

                } catch (error) {
                    clearInterval(statusInterval);
                    showError('Failed to get status');
                    resetForm();
                }
            }, 2000);
        }

        function updateProgress(status) {
            const progressText = document.getElementById('progressText');
            const progressFill = document.getElementById('progressFill');
            const progressStage = document.getElementById('progressStage');

            progressText.textContent = `Progress: ${status.progress || 0}%`;
            progressFill.style.width = `${status.progress || 0}%`;
            progressStage.textContent = status.stage || 'Processing...';

            if (status.total_pages) {
                progressStage.textContent += ` (${status.total_pages} pages)`;
            }

            if (status.elapsed_time) {
                progressStage.textContent += ` - ${status.elapsed_time}`;
            }
        }

        function showDownload() {
            const downloadSection = document.getElementById('downloadSection');
            const downloadBtn = document.getElementById('downloadBtn');

            downloadSection.style.display = 'block';
            downloadBtn.href = `/download/${currentJobId}`;

            resetForm();
        }

        function showError(message) {
            const errorMessage = document.getElementById('errorMessage');
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
        }

        function resetForm() {
            const submitBtn = document.getElementById('submitBtn');
            const progressContainer = document.getElementById('progressContainer');

            submitBtn.disabled = false;
            submitBtn.textContent = '🚀 Create Manga Recap Video';
            progressContainer.style.display = 'none';
        }
    </script>
</body>
</html>
