#!/usr/bin/env python3
"""
Streamlined Manga Recap System
PDF → Images → Manual Narrations → Audio → Video
No AI analysis - just pure processing speed
"""

import asyncio
import argparse
from pathlib import Path
from typing import List, Dict, Any
import sys
import time

# Import existing modules
from manga_extraction import extract_all_pages_as_images
from streamlined_director import create_streamlined_manga_movie

def print_streamlined_banner():
    """Print streamlined app banner"""
    print("🎯" + "="*60 + "🎯")
    print("🚀      STREAMLINED MANGA RECAP SYSTEM           🚀")
    print("🎯 PDF → Images → Manual Text → Audio → Video    🎯")
    print("⚡ No AI Analysis • Pure Speed • Perfect Control ⚡")
    print("💰              100% STREAMLINED                 💰")
    print("🎯" + "="*60 + "🎯")

async def create_streamlined_manga_recap(pdf_path: str, narrations_file: str, output_name: str) -> bool:
    """Create streamlined manga recap with manual narrations"""
    
    start_time = time.time()
    
    try:
        print_streamlined_banner()
        print(f"\n🎯 Starting Streamlined Manga Recap Creation")
        print(f"📖 PDF: {pdf_path}")
        print(f"📝 Narrations: {narrations_file}")
        print(f"🎯 Output: {output_name}")
        print(f"🔧 System: Pure Processing (No AI Analysis)")
        
        # Step 1: Extract pages
        print(f"\n📄 Step 1: Ultra-Fast Page Extraction...")
        extraction_start = time.time()
        
        extraction_result = extract_all_pages_as_images(pdf_path)
        
        if not extraction_result or 'scaled' not in extraction_result:
            print("❌ No pages extracted from PDF")
            return False
        
        base64_images = extraction_result['scaled']
        extraction_time = time.time() - extraction_start
        print(f"✅ Extracted {len(base64_images)} pages in {extraction_time:.1f}s")
        
        # Step 2: Load manual narrations
        print(f"\n📝 Step 2: Loading Manual Narrations...")
        narrations_start = time.time()
        
        try:
            with open(narrations_file, 'r', encoding='utf-8') as f:
                manual_narrations = f.read()
        except Exception as e:
            print(f"❌ Error reading narrations file: {e}")
            return False
        
        narrations_time = time.time() - narrations_start
        print(f"✅ Loaded narrations in {narrations_time:.1f}s")
        
        # Step 3: Create streamlined video
        print(f"\n🎯 Step 3: Streamlined Video Creation...")
        print("   📝 Processing manual narrations")
        print("   🗣️ Generating audio with Edge-TTS")
        print("   🎨 Creating videos with premium effects")
        print("   ⚡ Ultra-fast FFmpeg assembly")
        
        video_start = time.time()
        
        # Prepare output directory
        output_dir = Path(output_name).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create streamlined movie
        video_path = await create_streamlined_manga_movie(
            base64_images, 
            manual_narrations,
            str(output_dir), 
            Path(output_name).stem, 
            None
        )
        
        video_time = time.time() - video_start
        total_time = time.time() - start_time
        
        if video_path and Path(video_path).exists():
            print(f"\n🎉 STREAMLINED SUCCESS!")
            print(f"📁 Video: {video_path}")
            print(f"⏱️ Total time: {total_time:.1f}s")
            print(f"📊 Performance:")
            print(f"   📄 Extraction: {extraction_time:.1f}s")
            print(f"   📝 Narrations: {narrations_time:.1f}s")
            print(f"   🎯 Video creation: {video_time:.1f}s")
            print(f"   ⚡ Pages per second: {len(base64_images)/total_time:.1f}")
            print(f"💰 Cost: $0.00 (FREE!)")
            print(f"\n🌟 Streamlined Features:")
            print(f"   ✅ Manual narration control")
            print(f"   ✅ No AI dependencies")
            print(f"   ✅ Premium visual effects")
            print(f"   ✅ Perfect audio-video sync")
            print(f"   ✅ Ultra-fast processing")
            print(f"   ✅ 100% predictable results")
            
            return True
        else:
            print(f"❌ Video creation failed - file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error creating streamlined manga recap: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_sample_narrations_file(output_path: str, num_pages: int):
    """Create a sample narrations file"""
    
    sample_content = f"""Page 1
The story begins with our protagonist in a mysterious setting.

Page 2
The character explores their surroundings, taking in the new environment.

Page 3
A sense of wonder and curiosity fills the air as the journey continues.

"""
    
    # Add more sample pages
    for i in range(4, min(num_pages + 1, 11)):
        sample_content += f"""Page {i}
The adventure continues on page {i} with new discoveries and challenges.

"""
    
    if num_pages > 10:
        sample_content += f"""...
(Continue this pattern for all {num_pages} pages)

Page {num_pages}
The story reaches its conclusion with a satisfying ending.
"""
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print(f"📝 Sample narrations file created: {output_path}")
    print(f"   Edit this file with your actual narrations for all {num_pages} pages")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Streamlined Manga Recap System - Manual narrations for perfect control",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create sample narrations file
  python streamlined_app.py manga.pdf --create-sample narrations.txt
  
  # Create video with narrations
  python streamlined_app.py manga.pdf --narrations narrations.txt --output outputs/my_recap

Narration Format:
  Page 1
  Your narration for page 1 goes here.
  
  Page 2
  Your narration for page 2 goes here.
  
  (Continue for all pages...)

Features:
  • No AI dependencies - 100% predictable
  • Manual control over all narrations
  • Ultra-fast processing
  • Premium visual effects
  • Perfect audio-video synchronization
  • Professional quality output
        """
    )
    
    parser.add_argument(
        "pdf_path", 
        help="Path to the manga PDF file"
    )
    
    parser.add_argument(
        "--narrations", 
        help="Path to the narrations text file"
    )
    
    parser.add_argument(
        "--output", 
        help="Output name/path for the recap video (without extension)"
    )
    
    parser.add_argument(
        "--create-sample",
        help="Create a sample narrations file at the specified path"
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    pdf_path = Path(args.pdf_path)
    if not pdf_path.exists():
        print(f"❌ PDF file not found: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.suffix.lower() == '.pdf':
        print(f"❌ File must be a PDF: {pdf_path}")
        sys.exit(1)
    
    # Check for required tools
    import shutil
    if not shutil.which('ffmpeg'):
        print(f"❌ FFmpeg not found. Please install FFmpeg for ultra-fast processing.")
        print(f"   Download from: https://ffmpeg.org/download.html")
        sys.exit(1)
    
    # Handle sample creation
    if args.create_sample:
        print(f"📝 Creating sample narrations file...")
        
        # Extract pages to get count
        try:
            extraction_result = extract_all_pages_as_images(str(pdf_path))
            if extraction_result and 'scaled' in extraction_result:
                num_pages = len(extraction_result['scaled'])
                create_sample_narrations_file(args.create_sample, num_pages)
                print(f"\n💡 Next steps:")
                print(f"   1. Edit {args.create_sample} with your narrations")
                print(f"   2. Run: python streamlined_app.py {pdf_path} --narrations {args.create_sample} --output outputs/my_recap")
                sys.exit(0)
            else:
                print(f"❌ Could not extract pages from PDF")
                sys.exit(1)
        except Exception as e:
            print(f"❌ Error extracting pages: {e}")
            sys.exit(1)
    
    # Validate required arguments for video creation
    if not args.narrations:
        print(f"❌ Narrations file required. Use --create-sample to create a template.")
        sys.exit(1)
    
    if not args.output:
        print(f"❌ Output path required.")
        sys.exit(1)
    
    narrations_path = Path(args.narrations)
    if not narrations_path.exists():
        print(f"❌ Narrations file not found: {narrations_path}")
        print(f"   Use --create-sample to create a template.")
        sys.exit(1)
    
    # Run the streamlined recap creation
    try:
        success = asyncio.run(create_streamlined_manga_recap(
            str(pdf_path), 
            str(narrations_path),
            args.output
        ))
        
        if success:
            print(f"\n🎯 Streamlined manga recap completed successfully! 🎯")
            sys.exit(0)
        else:
            print(f"\n💥 Streamlined manga recap failed! 💥")
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⏹️ Process cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
