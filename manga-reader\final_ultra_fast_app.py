#!/usr/bin/env python3
"""
Final Ultra-Fast Manga Recap System
Simplified narration: LM Studio describes pages → Gemini creates 2-3 line narrations
"""

import asyncio
import argparse
from pathlib import Path
from typing import List, Dict, Any
import sys
import time
import os

# Import existing modules
from manga_extraction import extract_all_pages_as_images
from simple_ultra_fast_director import create_simple_ultra_fast_manga_movie

def print_final_banner():
    """Print final app banner"""
    print("🎯" + "="*60 + "🎯")
    print("🚀      FINAL ULTRA-FAST MANGA RECAP SYSTEM      🚀")
    print("🎯 Simple Narration • Lightning Speed • Perfect Quality 🎯")
    print("📖 LM Studio Vision → Gemini Narration → Ultra-Fast Video 📖")
    print("💰                100% OPTIMIZED                 💰")
    print("🎯" + "="*60 + "🎯")

async def create_final_manga_recap(pdf_path: str, output_name: str, gemini_api_key: str = None) -> bool:
    """Create final optimized manga recap"""
    
    start_time = time.time()
    
    try:
        print_final_banner()
        print(f"\n🎯 Starting Final Ultra-Fast Manga Recap Creation")
        print(f"📖 Processing: {pdf_path}")
        print(f"🎯 Output: {output_name}")
        print(f"🔧 System: LM Studio + Gemini + OpenCV + FFmpeg")
        print(f"🗣️ Narration: Simplified 2-3 line per page system")
        
        if gemini_api_key:
            print(f"🤖 Gemini API: Configured for optimal narration")
        else:
            print(f"⚠️ Gemini API: Not configured (will use LM Studio fallback)")
            print(f"   💡 For best results, set GEMINI_API_KEY environment variable")
        
        # Step 1: Extract pages
        print(f"\n📄 Step 1: Ultra-Fast Page Extraction...")
        extraction_start = time.time()
        
        extraction_result = extract_all_pages_as_images(pdf_path)
        
        if not extraction_result or 'scaled' not in extraction_result:
            print("❌ No pages extracted from PDF")
            return False
        
        base64_images = extraction_result['scaled']
        extraction_time = time.time() - extraction_start
        print(f"✅ Extracted {len(base64_images)} pages in {extraction_time:.1f}s")
        
        # Step 2: Create final video with simplified narration
        print(f"\n🎯 Step 2: Final Ultra-Fast Video Creation...")
        print("   📖 LM Studio: Analyzing each page for scene + text")
        print("   🤖 Gemini: Creating 2-3 line narrations per page")
        print("   🎨 OpenCV: Premium effects with blurred backgrounds")
        print("   ⚡ FFmpeg: Lightning-fast assembly")
        
        video_start = time.time()
        
        # Prepare output directory
        output_dir = Path(output_name).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Create final movie with simplified system
        video_path = await create_simple_ultra_fast_manga_movie(
            base64_images, 
            str(output_dir), 
            Path(output_name).stem, 
            None,
            gemini_api_key=gemini_api_key
        )
        
        video_time = time.time() - video_start
        total_time = time.time() - start_time
        
        if video_path and Path(video_path).exists():
            print(f"\n🎉 FINAL ULTRA-FAST SUCCESS!")
            print(f"📁 Video: {video_path}")
            print(f"⏱️ Total time: {total_time:.1f}s")
            print(f"📊 Performance:")
            print(f"   📄 Extraction: {extraction_time:.1f}s")
            print(f"   🎯 Video creation: {video_time:.1f}s")
            print(f"   ⚡ Pages per second: {len(base64_images)/total_time:.1f}")
            print(f"💰 Cost: $0.00 (FREE!)")
            print(f"\n🌟 Final Features:")
            print(f"   ✅ Simple, effective narration (2-3 lines per page)")
            print(f"   ✅ LM Studio vision analysis")
            print(f"   ✅ Gemini-powered storytelling")
            print(f"   ✅ Premium visual effects")
            print(f"   ✅ Perfect audio-video sync")
            print(f"   ✅ Ultra-fast processing")
            
            return True
        else:
            print(f"❌ Video creation failed - file not found")
            return False
            
    except Exception as e:
        print(f"❌ Error creating final manga recap: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description="Final Ultra-Fast Manga Recap System - Optimized narration and speed",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python final_ultra_fast_app.py manga.pdf --output outputs/my_recap
  python final_ultra_fast_app.py manga.pdf --output outputs/my_recap --gemini-key YOUR_API_KEY

Narration System:
  1. LM Studio analyzes each page (scene description + text extraction)
  2. All page data sent to Gemini for 2-3 line narrations per page
  3. Clean, engaging narrations perfect for voice synthesis
  
Speed Optimizations:
  • Simplified narration workflow (no complex character tracking)
  • OpenCV for ultra-fast image processing
  • FFmpeg for lightning-fast video assembly
  • Parallel processing for all operations
  • Per-page video creation for perfect sync
  
Premium Features:
  • Blurred background effects
  • Perfect audio-video synchronization
  • Professional quality output
  • Natural voice narration with Edge-TTS
        """
    )
    
    parser.add_argument(
        "pdf_path", 
        help="Path to the manga PDF file"
    )
    
    parser.add_argument(
        "--output", 
        required=True,
        help="Output name/path for the recap video (without extension)"
    )
    
    parser.add_argument(
        "--gemini-key",
        help="Gemini API key for optimal narration (or set GEMINI_API_KEY env var)"
    )
    
    args = parser.parse_args()
    
    # Validate inputs
    pdf_path = Path(args.pdf_path)
    if not pdf_path.exists():
        print(f"❌ PDF file not found: {pdf_path}")
        sys.exit(1)
    
    if not pdf_path.suffix.lower() == '.pdf':
        print(f"❌ File must be a PDF: {pdf_path}")
        sys.exit(1)
    
    # Check for required tools
    import shutil
    if not shutil.which('ffmpeg'):
        print(f"❌ FFmpeg not found. Please install FFmpeg for ultra-fast processing.")
        print(f"   Download from: https://ffmpeg.org/download.html")
        sys.exit(1)
    
    # Get Gemini API key
    gemini_api_key = args.gemini_key or os.getenv('GEMINI_API_KEY')
    
    if not gemini_api_key:
        print(f"\n💡 TIP: For best narration quality, get a free Gemini API key:")
        print(f"   1. Go to: https://makersuite.google.com/app/apikey")
        print(f"   2. Create a free API key")
        print(f"   3. Set environment variable: set GEMINI_API_KEY=your_key")
        print(f"   4. Or use: --gemini-key your_key")
        print(f"\n   Proceeding with LM Studio fallback narration...\n")
    
    # Run the final recap creation
    try:
        success = asyncio.run(create_final_manga_recap(
            str(pdf_path), 
            args.output, 
            gemini_api_key
        ))
        
        if success:
            print(f"\n🎯 Final ultra-fast manga recap completed successfully! 🎯")
            sys.exit(0)
        else:
            print(f"\n💥 Final manga recap failed! 💥")
            sys.exit(1)
    except KeyboardInterrupt:
        print(f"\n⏹️ Process cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
