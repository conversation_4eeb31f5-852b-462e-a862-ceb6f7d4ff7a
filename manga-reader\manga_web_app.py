#!/usr/bin/env python3
"""
Manga Recap Web Application
Beautiful frontend for PDF upload and narration processing
"""

from flask import Flask, render_template, request, jsonify, send_file
import os
import asyncio
import threading
import time
from pathlib import Path
import uuid
from werkzeug.utils import secure_filename

# Import our processing modules
from manga_extraction import extract_all_pages_as_images
from streamlined_director import create_streamlined_manga_movie

app = Flask(__name__)
app.config['SECRET_KEY'] = 'manga-recap-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'pdf'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# Global storage for processing status
processing_status = {}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def run_async_processing(job_id, pdf_path, narrations_text, output_name):
    """Run the async processing in a separate thread"""
    try:
        processing_status[job_id]['status'] = 'processing'
        processing_status[job_id]['stage'] = 'Extracting pages from PDF...'
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            process_manga_recap(job_id, pdf_path, narrations_text, output_name)
        )
        
        loop.close()
        
        if result:
            processing_status[job_id]['status'] = 'completed'
            processing_status[job_id]['stage'] = 'Video creation completed!'
            processing_status[job_id]['video_path'] = result
        else:
            processing_status[job_id]['status'] = 'error'
            processing_status[job_id]['stage'] = 'Processing failed'
            
    except Exception as e:
        processing_status[job_id]['status'] = 'error'
        processing_status[job_id]['stage'] = f'Error: {str(e)}'

async def process_manga_recap(job_id, pdf_path, narrations_text, output_name):
    """Process the manga recap with status updates"""
    
    try:
        # Step 1: Extract pages
        processing_status[job_id]['stage'] = 'Extracting pages from PDF...'
        processing_status[job_id]['progress'] = 10
        
        extraction_result = extract_all_pages_as_images(pdf_path)
        
        if not extraction_result or 'scaled' not in extraction_result:
            raise Exception("Failed to extract pages from PDF")
        
        base64_images = extraction_result['scaled']
        total_pages = len(base64_images)
        
        processing_status[job_id]['stage'] = f'Extracted {total_pages} pages successfully'
        processing_status[job_id]['progress'] = 30
        processing_status[job_id]['total_pages'] = total_pages
        
        # Step 2: Create video
        processing_status[job_id]['stage'] = 'Creating manga recap video...'
        processing_status[job_id]['progress'] = 40
        
        # Prepare output directory
        output_dir = os.path.join(OUTPUT_FOLDER, job_id)
        os.makedirs(output_dir, exist_ok=True)
        
        # Create streamlined movie
        video_path = await create_streamlined_manga_movie(
            base64_images, 
            narrations_text,
            output_dir, 
            output_name, 
            None
        )
        
        processing_status[job_id]['progress'] = 100
        
        if video_path and os.path.exists(video_path):
            return video_path
        else:
            raise Exception("Video creation failed")
            
    except Exception as e:
        processing_status[job_id]['stage'] = f'Error: {str(e)}'
        processing_status[job_id]['status'] = 'error'
        return None

@app.route('/')
def index():
    """Main page"""
    return render_template('manga_index.html')

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads"""
    
    if 'pdf_file' not in request.files:
        return jsonify({'error': 'No PDF file uploaded'}), 400
    
    pdf_file = request.files['pdf_file']
    narrations_text = request.form.get('narrations_text', '').strip()
    
    if pdf_file.filename == '':
        return jsonify({'error': 'No PDF file selected'}), 400
    
    if not narrations_text:
        return jsonify({'error': 'No narrations provided'}), 400
    
    if pdf_file and allowed_file(pdf_file.filename):
        job_id = str(uuid.uuid4())
        
        filename = secure_filename(pdf_file.filename)
        pdf_path = os.path.join(UPLOAD_FOLDER, f"{job_id}_{filename}")
        pdf_file.save(pdf_path)
        
        processing_status[job_id] = {
            'status': 'queued',
            'stage': 'Preparing to process...',
            'progress': 0,
            'total_pages': 0,
            'video_path': None,
            'created_at': time.time()
        }
        
        output_name = Path(filename).stem
        thread = threading.Thread(
            target=run_async_processing,
            args=(job_id, pdf_path, narrations_text, output_name)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'job_id': job_id,
            'message': 'Processing started successfully'
        })
    
    return jsonify({'error': 'Invalid file type. Please upload a PDF file.'}), 400

@app.route('/status/<job_id>')
def get_status(job_id):
    """Get processing status"""
    
    if job_id not in processing_status:
        return jsonify({'error': 'Job not found'}), 404
    
    status = processing_status[job_id].copy()
    elapsed = time.time() - status['created_at']
    status['elapsed_time'] = f"{elapsed:.1f}s"
    
    return jsonify(status)

@app.route('/download/<job_id>')
def download_video(job_id):
    """Download the completed video"""
    
    if job_id not in processing_status:
        return jsonify({'error': 'Job not found'}), 404
    
    status = processing_status[job_id]
    
    if status['status'] != 'completed' or not status['video_path']:
        return jsonify({'error': 'Video not ready'}), 400
    
    video_path = status['video_path']
    
    if not os.path.exists(video_path):
        return jsonify({'error': 'Video file not found'}), 404
    
    return send_file(
        video_path,
        as_attachment=True,
        download_name=f"manga_recap_{job_id}.mp4",
        mimetype='video/mp4'
    )

if __name__ == '__main__':
    print("🌟 Starting Manga Recap Web Application...")
    print("📱 Access the web interface at: http://localhost:5000")
    print("🎯 Upload your PDF and narrations to create professional manga recaps!")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)
