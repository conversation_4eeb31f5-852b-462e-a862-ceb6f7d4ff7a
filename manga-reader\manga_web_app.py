#!/usr/bin/env python3
"""
Manga Recap Web Application
Beautiful frontend for PDF upload and narration processing
"""

from flask import Flask, render_template, request, jsonify, send_file
import os
import asyncio
import threading
import time
from pathlib import Path
import uuid
from werkzeug.utils import secure_filename

# Import our processing modules
from manga_extraction import extract_all_pages_as_images
from streamlined_director import create_streamlined_manga_movie
import aiohttp
import json
import re

app = Flask(__name__)
app.config['SECRET_KEY'] = 'manga-recap-secret-key'
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size

# Configuration
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
ALLOWED_EXTENSIONS = {'pdf'}

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# Global storage for processing status
processing_status = {}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

async def generate_narration_with_qwen(base64_image: str, image_number, context: str = "", is_group: bool = False) -> dict:
    """Generate narration for a single image using Qwen vision"""

    if is_group:
        page_list = ", ".join([f"Page {p}" for p in image_number]) if isinstance(image_number, list) else f"Pages {image_number}"
        prompt = f"""You are narrating 3 manga pages shown side by side (left to right). Be VERY concise.

RULES:
1. If pages have text/dialogue: Just state the text in reading order (left to right)
2. If pages have NO text: Briefly describe what happens in 1-2 sentences
3. Keep narration to 2-3 lines maximum
4. NO visual descriptions like "the image shows" or "black text box"
5. Focus ONLY on story content

Context: {context if context else "Story beginning."}

Respond in JSON:
{{
    "pages": "{page_list}",
    "narration": "Brief 2-3 line narration"
}}

Example: "Hello world. How are you? The character walks forward.\""""
    else:
        prompt = f"""You are creating narrations for a manga recap video. For Page {image_number}:

RULES:
1. If the image contains text/dialogue: Just state the text exactly as written. Do NOT describe the visual appearance, background, or layout.
2. If the image has NO text: Briefly describe what is happening in the scene.
3. Do NOT mention sound effects, visual descriptions of text boxes, or unnecessary details.
4. Keep it concise and focused.

Context: {context if context else "Beginning of story."}

Respond in JSON format:
{{
    "image_number": {image_number},
    "narration": "Your narration here"
}}

Examples:
- If image shows text "HELLO WORLD": narration should be "HELLO WORLD"
- If image shows character walking: narration should be "The character walks forward"
- Do NOT say things like "The text reads" or "black square with white text" - just give the actual content."""

    payload = {
        "model": "qwen2-vl-2b-instruct",
        "messages": [
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {"type": "image_url", "image_url": {"url": f"data:image/png;base64,{base64_image}"}}
                ]
            }
        ],
        "temperature": 0.3,
        "max_tokens": 500
    }

    try:
        # Debug: Log the request details
        if is_group:
            print(f"   🤖 Sending combined image to LM Studio for pages {image_number}")
            print(f"   📏 Image data size: {len(base64_image)} characters")

        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:1234/v1/chat/completions",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=60)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result['choices'][0]['message']['content']

                    # Try to extract JSON from the response
                    try:
                        # Look for JSON in the response (handle both single page and group formats)
                        if is_group:
                            json_match = re.search(r'\{[^}]*"pages"[^}]*"narration"[^}]*\}', content, re.DOTALL)
                        else:
                            json_match = re.search(r'\{[^}]*"image_number"[^}]*"narration"[^}]*\}', content, re.DOTALL)

                        if json_match:
                            json_str = json_match.group(0)
                            parsed = json.loads(json_str)
                            narration = parsed.get('narration', '').strip()

                            # Clean up the narration - remove unnecessary descriptions
                            narration = re.sub(r'^(Image \d+\s*[:\-]?\s*|Pages? [^:]*[:\-]?\s*)', '', narration)
                            narration = re.sub(r'The (image|text|black square|white text).*?reads,?\s*["\']?', '', narration, flags=re.IGNORECASE)
                            narration = re.sub(r'["\']([^"\']+)["\']', r'\1', narration)  # Remove quotes around text
                            narration = re.sub(r'\s+', ' ', narration).strip()  # Clean whitespace

                            return {
                                'success': True,
                                'image_number': image_number,
                                'narration': narration
                            }
                        else:
                            # Try to parse as direct JSON
                            try:
                                parsed = json.loads(content)
                                narration = parsed.get('narration', content).strip()
                                return {
                                    'success': True,
                                    'image_number': image_number,
                                    'narration': narration
                                }
                            except:
                                # Fallback: clean the raw content
                                if is_group:
                                    page_ref = f"Pages {image_number[0]}-{image_number[-1]}" if isinstance(image_number, list) else f"Pages {image_number}"
                                else:
                                    page_ref = f"Page {image_number}"
                                narration = content.replace(page_ref, "").strip()
                                narration = re.sub(r'The (image|text).*?reads,?\s*["\']?', '', narration, flags=re.IGNORECASE)
                                return {
                                    'success': True,
                                    'image_number': image_number,
                                    'narration': narration
                                }
                    except json.JSONDecodeError:
                        # Fallback: clean the raw content
                        if is_group:
                            page_ref = f"Pages {image_number[0]}-{image_number[-1]}" if isinstance(image_number, list) else f"Pages {image_number}"
                        else:
                            page_ref = f"Page {image_number}"
                        narration = content.replace(page_ref, "").strip()
                        narration = re.sub(r'The (image|text).*?reads,?\s*["\']?', '', narration, flags=re.IGNORECASE)
                        return {
                            'success': True,
                            'image_number': image_number,
                            'narration': narration
                        }
                else:
                    return {
                        'success': False,
                        'error': f"LM Studio error: {response.status}"
                    }

    except Exception as e:
        return {
            'success': False,
            'error': f"Error connecting to LM Studio: {str(e)}"
        }

def run_narration_generation(job_id, pdf_path):
    """Run narration generation in a separate thread"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        result = loop.run_until_complete(
            generate_all_narrations(job_id, pdf_path)
        )

        loop.close()

        if result:
            processing_status[job_id]['status'] = 'narrations_completed'
            processing_status[job_id]['stage'] = 'Narrations generated successfully!'
        else:
            processing_status[job_id]['status'] = 'error'
            processing_status[job_id]['stage'] = 'Narration generation failed'

    except Exception as e:
        processing_status[job_id]['status'] = 'error'
        processing_status[job_id]['stage'] = f'Error: {str(e)}'

def combine_images_horizontally(base64_images):
    """Combine 3 base64 images horizontally into one image"""
    from PIL import Image
    import io
    import base64

    # Decode base64 images
    pil_images = []
    for b64_img in base64_images:
        img_data = base64.b64decode(b64_img)
        pil_img = Image.open(io.BytesIO(img_data))
        pil_images.append(pil_img)

    # Get dimensions (assume all images have same height)
    width, height = pil_images[0].size

    # Create combined image (3 times the width)
    combined_width = width * len(pil_images)
    combined_image = Image.new('RGB', (combined_width, height), 'white')

    # Paste images side by side
    for i, img in enumerate(pil_images):
        combined_image.paste(img, (i * width, 0))

    # Convert back to base64
    buffer = io.BytesIO()
    combined_image.save(buffer, format='PNG')
    combined_b64 = base64.b64encode(buffer.getvalue()).decode('utf-8')

    return combined_b64

async def generate_all_narrations(job_id, pdf_path):
    """Generate narrations for page groups (3 pages at a time)"""

    try:
        # Step 1: Extract pages
        processing_status[job_id]['stage'] = 'Extracting pages from PDF...'
        processing_status[job_id]['progress'] = 10

        extraction_result = extract_all_pages_as_images(pdf_path)

        if not extraction_result or 'scaled' not in extraction_result:
            raise Exception("Failed to extract pages from PDF")

        base64_images = extraction_result['scaled']
        total_pages = len(base64_images)

        processing_status[job_id]['stage'] = f'Extracted {total_pages} pages'
        processing_status[job_id]['progress'] = 20
        processing_status[job_id]['total_pages'] = total_pages

        # Step 2: Group pages into sets of 3 and generate narrations
        processing_status[job_id]['stage'] = 'Grouping pages and generating narrations...'

        narrations = {}
        context = ""

        # Process pages in groups of 3
        for i in range(0, total_pages, 3):
            group_start = i + 1
            group_end = min(i + 3, total_pages)
            group_pages = list(range(group_start, group_end + 1))

            processing_status[job_id]['stage'] = f'Generating narrations for pages {group_start}-{group_end}'
            processing_status[job_id]['progress'] = 20 + (70 * i / total_pages)

            # Get images for this group
            group_images = base64_images[i:i+3]

            # If we have less than 3 images, pad with the last image
            while len(group_images) < 3 and len(group_images) > 0:
                group_images.append(group_images[-1])

            if len(group_images) == 0:
                break

            # Combine images horizontally
            combined_image = combine_images_horizontally(group_images)

            # Debug: Check combined image size
            print(f"   🔍 Combined image for pages {group_start}-{group_end}: {len(combined_image)} bytes")

            # Create page list string for the prompt
            page_list = ", ".join([f"Page {p}" for p in group_pages])

            # Generate narration for this group
            result = await generate_narration_with_qwen(combined_image, group_pages, context, is_group=True)

            if result['success']:
                group_narration = result['narration']

                # Store the group narration with a special key for the group
                group_key = f"group_{group_start}_{group_end}"
                narrations[group_key] = group_narration

                # Update context
                if len(context.split()) > 50:  # Limit context
                    context_words = context.split()
                    context = ' '.join(context_words[-30:]) + f" {group_narration}"
                else:
                    context += f" {group_narration}"

                print(f"   ✅ Generated narration for pages {group_start}-{group_end}: {group_narration[:50]}...")
            else:
                # Fallback narration for the group
                group_key = f"group_{group_start}_{group_end}"
                narrations[group_key] = f"The story continues across pages {group_start}-{group_end}."
                print(f"   ⚠️ Failed to generate narration for pages {group_start}-{group_end}: {result.get('error', 'Unknown error')}")

        processing_status[job_id]['progress'] = 100
        processing_status[job_id]['narrations'] = narrations

        return True

    except Exception as e:
        processing_status[job_id]['stage'] = f'Error: {str(e)}'
        processing_status[job_id]['status'] = 'error'
        return False

def run_async_processing(job_id, pdf_path, narrations_text, output_name):
    """Run the async processing in a separate thread"""
    try:
        processing_status[job_id]['status'] = 'processing'
        processing_status[job_id]['stage'] = 'Extracting pages from PDF...'
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            process_manga_recap(job_id, pdf_path, narrations_text, output_name)
        )
        
        loop.close()
        
        if result:
            processing_status[job_id]['status'] = 'completed'
            processing_status[job_id]['stage'] = 'Video creation completed!'
            processing_status[job_id]['video_path'] = result
        else:
            processing_status[job_id]['status'] = 'error'
            processing_status[job_id]['stage'] = 'Processing failed'
            
    except Exception as e:
        processing_status[job_id]['status'] = 'error'
        processing_status[job_id]['stage'] = f'Error: {str(e)}'

async def process_manga_recap(job_id, pdf_path, narrations_text, output_name):
    """Process the manga recap with status updates"""
    
    try:
        # Step 1: Extract pages
        processing_status[job_id]['stage'] = 'Extracting pages from PDF...'
        processing_status[job_id]['progress'] = 10
        
        extraction_result = extract_all_pages_as_images(pdf_path)
        
        if not extraction_result or 'scaled' not in extraction_result:
            raise Exception("Failed to extract pages from PDF")
        
        base64_images = extraction_result['scaled']
        total_pages = len(base64_images)
        
        processing_status[job_id]['stage'] = f'Extracted {total_pages} pages successfully'
        processing_status[job_id]['progress'] = 30
        processing_status[job_id]['total_pages'] = total_pages
        
        # Step 2: Create video
        processing_status[job_id]['stage'] = 'Creating manga recap video...'
        processing_status[job_id]['progress'] = 40
        
        # Prepare output directory
        output_dir = os.path.join(OUTPUT_FOLDER, job_id)
        os.makedirs(output_dir, exist_ok=True)
        
        # Create streamlined movie
        video_path = await create_streamlined_manga_movie(
            base64_images, 
            narrations_text,
            output_dir, 
            output_name, 
            None
        )
        
        processing_status[job_id]['progress'] = 100
        
        if video_path and os.path.exists(video_path):
            return video_path
        else:
            raise Exception("Video creation failed")
            
    except Exception as e:
        processing_status[job_id]['stage'] = f'Error: {str(e)}'
        processing_status[job_id]['status'] = 'error'
        return None

@app.route('/')
def index():
    """Main page"""
    return render_template('manga_index.html')

@app.route('/upload', methods=['POST'])
def upload_files():
    """Handle file uploads"""
    
    if 'pdf_file' not in request.files:
        return jsonify({'error': 'No PDF file uploaded'}), 400
    
    pdf_file = request.files['pdf_file']
    narrations_text = request.form.get('narrations_text', '').strip()
    
    if pdf_file.filename == '':
        return jsonify({'error': 'No PDF file selected'}), 400
    
    if not narrations_text:
        return jsonify({'error': 'No narrations provided'}), 400
    
    if pdf_file and allowed_file(pdf_file.filename):
        job_id = str(uuid.uuid4())
        
        filename = secure_filename(pdf_file.filename)
        pdf_path = os.path.join(UPLOAD_FOLDER, f"{job_id}_{filename}")
        pdf_file.save(pdf_path)
        
        processing_status[job_id] = {
            'status': 'queued',
            'stage': 'Preparing to process...',
            'progress': 0,
            'total_pages': 0,
            'video_path': None,
            'created_at': time.time()
        }
        
        output_name = Path(filename).stem
        thread = threading.Thread(
            target=run_async_processing,
            args=(job_id, pdf_path, narrations_text, output_name)
        )
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'job_id': job_id,
            'message': 'Processing started successfully'
        })
    
    return jsonify({'error': 'Invalid file type. Please upload a PDF file.'}), 400

@app.route('/status/<job_id>')
def get_status(job_id):
    """Get processing status"""
    
    if job_id not in processing_status:
        return jsonify({'error': 'Job not found'}), 404
    
    status = processing_status[job_id].copy()
    elapsed = time.time() - status['created_at']
    status['elapsed_time'] = f"{elapsed:.1f}s"
    
    return jsonify(status)

@app.route('/generate_narrations', methods=['POST'])
def generate_narrations():
    """Generate narrations using LM Studio Qwen vision"""

    if 'pdf_file' not in request.files:
        return jsonify({'error': 'No PDF file uploaded'}), 400

    pdf_file = request.files['pdf_file']

    if pdf_file.filename == '':
        return jsonify({'error': 'No PDF file selected'}), 400

    if pdf_file and allowed_file(pdf_file.filename):
        try:
            # Generate unique job ID for this narration generation
            job_id = str(uuid.uuid4())

            # Save uploaded file temporarily
            filename = secure_filename(pdf_file.filename)
            pdf_path = os.path.join(UPLOAD_FOLDER, f"{job_id}_{filename}")
            pdf_file.save(pdf_path)

            # Initialize processing status for narration generation
            processing_status[job_id] = {
                'status': 'generating_narrations',
                'stage': 'Extracting pages from PDF...',
                'progress': 0,
                'total_pages': 0,
                'narrations': {},
                'created_at': time.time()
            }

            # Start narration generation in background
            thread = threading.Thread(
                target=run_narration_generation,
                args=(job_id, pdf_path)
            )
            thread.daemon = True
            thread.start()

            return jsonify({
                'success': True,
                'job_id': job_id,
                'message': 'Narration generation started'
            })

        except Exception as e:
            return jsonify({'error': f'Error starting narration generation: {str(e)}'}), 500

    return jsonify({'error': 'Invalid file type. Please upload a PDF file.'}), 400

@app.route('/get_narrations/<job_id>')
def get_narrations(job_id):
    """Get generated narrations"""

    if job_id not in processing_status:
        return jsonify({'error': 'Job not found'}), 404

    status = processing_status[job_id]

    if status['status'] != 'narrations_completed':
        return jsonify({'error': 'Narrations not ready'}), 400

    # Format narrations as text
    narrations_text = ""
    narrations = status.get('narrations', {})

    # Sort group keys properly
    group_keys = [k for k in narrations.keys() if k.startswith('group_')]
    group_keys.sort(key=lambda x: int(x.split('_')[1]))  # Sort by start page number

    page_counter = 1
    for group_key in group_keys:
        # Extract group info
        parts = group_key.split('_')
        group_start = int(parts[1])
        group_end = int(parts[2])

        # Add narration for each page in the group
        for page_num in range(group_start, group_end + 1):
            narrations_text += f"Page {page_num}\n{narrations[group_key]}\n\n"

    return jsonify({
        'success': True,
        'narrations_text': narrations_text.strip(),
        'total_pages': len(narrations)
    })

@app.route('/download/<job_id>')
def download_video(job_id):
    """Download the completed video"""

    if job_id not in processing_status:
        return jsonify({'error': 'Job not found'}), 404

    status = processing_status[job_id]

    if status['status'] != 'completed' or not status['video_path']:
        return jsonify({'error': 'Video not ready'}), 400

    video_path = status['video_path']

    if not os.path.exists(video_path):
        return jsonify({'error': 'Video file not found'}), 404

    return send_file(
        video_path,
        as_attachment=True,
        download_name=f"manga_recap_{job_id}.mp4",
        mimetype='video/mp4'
    )

if __name__ == '__main__':
    print("🌟 Starting Manga Recap Web Application...")
    print("📱 Access the web interface at: http://localhost:5000")
    print("🎯 Upload your PDF and narrations to create professional manga recaps!")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)
