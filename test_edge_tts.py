#!/usr/bin/env python3
"""
Test script to verify Edge-TTS functionality
"""
import asyncio
import edge_tts
from io import BytesIO

async def test_edge_tts():
    """Test Edge-TTS text-to-speech generation"""
    print("Testing Edge-TTS...")
    
    # Test text
    test_text = "Hello! This is a test of the Edge-TTS text-to-speech system. It should sound natural and clear."
    
    # Use a high-quality voice
    voice = "en-US-AriaNeural"
    
    try:
        # Create Edge-TTS communication object
        communicate = edge_tts.Communicate(test_text, voice)
        
        # Generate audio and collect bytes
        audio_bytes_io = BytesIO()
        async for chunk in communicate.stream():
            if chunk["type"] == "audio":
                audio_bytes_io.write(chunk["data"])
        
        # Reset position to beginning
        audio_bytes_io.seek(0)
        
        # Check if we got audio data
        audio_size = audio_bytes_io.getbuffer().nbytes
        print(f"✅ Successfully generated {audio_size} bytes of audio")
        
        # Save test audio file
        with open("test_audio.mp3", "wb") as f:
            f.write(audio_bytes_io.getvalue())
        
        print("✅ Test audio saved as 'test_audio.mp3'")
        print("✅ Edge-TTS is working correctly!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Edge-TTS: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_edge_tts())
    if success:
        print("\n🎉 Edge-TTS test passed!")
    else:
        print("\n❌ Edge-TTS test failed!")
