import re
from typing import Dict, List, Tuple

class SimpleTextProcessor:
    """Simple text processor for manual narrations"""
    
    def __init__(self):
        pass
    
    def parse_manual_narrations(self, narration_text: str) -> Dict[int, str]:
        """Parse manual narrations in the format:
        Page 24
        The person resolves to accept their situation and move forward.
        
        Page 25
        The view is of a tunnel made of concentric circles...
        """
        
        narrations = {}
        
        # Split by "Page" keyword
        sections = re.split(r'\n(?=Page \d+)', narration_text.strip())
        
        for section in sections:
            section = section.strip()
            if not section:
                continue
                
            # Extract page number and narration
            lines = section.split('\n', 1)
            if len(lines) < 2:
                continue
                
            # Parse page number
            page_line = lines[0].strip()
            page_match = re.match(r'Page (\d+)', page_line)
            
            if not page_match:
                continue
                
            page_num = int(page_match.group(1))
            
            # Get narration text (everything after the page line)
            narration = lines[1].strip()
            
            # Clean up narration
            narration = self._clean_narration(narration)
            
            narrations[page_num] = narration
            
        return narrations
    
    def _clean_narration(self, text: str) -> str:
        """Clean up narration text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Ensure proper sentence ending
        text = text.strip()
        if text and not text.endswith(('.', '!', '?')):
            text += '.'
            
        return text
    
    def validate_narrations(self, narrations: Dict[int, str], total_pages: int) -> Tuple[bool, List[str]]:
        """Validate that narrations cover all pages"""
        
        errors = []
        
        # Check for missing pages
        missing_pages = []
        for page_num in range(1, total_pages + 1):
            if page_num not in narrations:
                missing_pages.append(page_num)
        
        if missing_pages:
            errors.append(f"Missing narrations for pages: {missing_pages}")
        
        # Check for extra pages
        extra_pages = []
        for page_num in narrations.keys():
            if page_num > total_pages or page_num < 1:
                extra_pages.append(page_num)
        
        if extra_pages:
            errors.append(f"Extra narrations for invalid pages: {extra_pages}")
        
        # Check for empty narrations
        empty_pages = []
        for page_num, narration in narrations.items():
            if not narration.strip():
                empty_pages.append(page_num)
        
        if empty_pages:
            errors.append(f"Empty narrations for pages: {empty_pages}")
        
        is_valid = len(errors) == 0
        return is_valid, errors
    
    def get_narration_stats(self, narrations: Dict[int, str]) -> Dict[str, any]:
        """Get statistics about the narrations"""
        
        if not narrations:
            return {
                'total_pages': 0,
                'total_words': 0,
                'avg_words_per_page': 0,
                'shortest_narration': '',
                'longest_narration': '',
                'page_range': (0, 0)
            }
        
        total_words = 0
        word_counts = []
        
        for narration in narrations.values():
            words = len(narration.split())
            word_counts.append(words)
            total_words += words
        
        avg_words = total_words / len(narrations) if narrations else 0
        
        # Find shortest and longest narrations
        shortest_page = min(narrations.keys(), key=lambda p: len(narrations[p].split()))
        longest_page = max(narrations.keys(), key=lambda p: len(narrations[p].split()))
        
        page_numbers = list(narrations.keys())
        page_range = (min(page_numbers), max(page_numbers)) if page_numbers else (0, 0)
        
        return {
            'total_pages': len(narrations),
            'total_words': total_words,
            'avg_words_per_page': round(avg_words, 1),
            'shortest_narration': f"Page {shortest_page}: {narrations[shortest_page][:50]}...",
            'longest_narration': f"Page {longest_page}: {narrations[longest_page][:50]}...",
            'page_range': page_range,
            'word_counts': word_counts
        }

# Example usage and testing
def test_text_processor():
    """Test the text processor with sample data"""
    
    sample_text = """Page 24
The person resolves to accept their situation and move forward.

Page 25
The view is of a tunnel made of concentric circles of gray and black, with a light at the center.

Page 26
The perspective moves through the tunnel of light.

Page 27
The light at the end of the tunnel grows larger and brighter.

Page 28
The white light at the center of the tunnel expands, getting closer.

Page 29
CONGRATULATIONS, SIR AND MADAM. HE'S A HEALTHY BOY!

Page 30
From a first-person perspective, the words of congratulation are heard as a blurry face looks on."""
    
    processor = SimpleTextProcessor()
    narrations = processor.parse_manual_narrations(sample_text)
    
    print("📖 Parsed Narrations:")
    for page_num in sorted(narrations.keys()):
        print(f"   Page {page_num}: {narrations[page_num]}")
    
    stats = processor.get_narration_stats(narrations)
    print(f"\n📊 Statistics:")
    print(f"   Total pages: {stats['total_pages']}")
    print(f"   Total words: {stats['total_words']}")
    print(f"   Average words per page: {stats['avg_words_per_page']}")
    print(f"   Page range: {stats['page_range'][0]}-{stats['page_range'][1]}")
    
    return narrations

if __name__ == "__main__":
    test_text_processor()
