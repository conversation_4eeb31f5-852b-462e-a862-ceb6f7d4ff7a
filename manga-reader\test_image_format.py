#!/usr/bin/env python3
"""
Test script to verify image format being sent to LM Studio
"""
import requests
import json
import base64
from io import BytesIO
from PIL import Image

# Test configuration
LM_STUDIO_URL = "http://localhost:1234"
QWEN_VISION_MODEL = "qwen2-vl-2b-instruct"

def create_test_image():
    """Create a simple test image and return base64"""
    # Create a simple test image with text
    img = Image.new('RGB', (200, 100), color='white')
    
    # Convert to base64 (PNG format)
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
    
    return img_base64

def test_exact_lm_studio_format():
    """Test the exact format from LM Studio documentation"""
    print("🧪 Testing exact LM Studio image format...")
    
    # Create test image
    test_image = create_test_image()
    
    # Use the exact format from LM Studio docs
    payload = {
        "model": QWEN_VISION_MODEL,
        "messages": [
            { 
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "What do you see in this image? Respond with just a brief description."
                    },
                    {
                        "type": "image_url",
                        "image_url": { 
                            "url": f"data:image/png;base64,{test_image}" 
                        }
                    }
                ]
            }
        ],
        "temperature": 0.1,
        "max_tokens": 100,
        "stream": False
    }
    
    print("📤 Sending request to LM Studio...")
    print(f"🔍 Image data length: {len(test_image)} characters")
    print(f"🔍 Image data starts with: {test_image[:50]}...")
    
    try:
        response = requests.post(
            f"{LM_STUDIO_URL}/v1/chat/completions",
            headers={"Content-Type": "application/json"},
            json=payload,
            timeout=60
        )
        
        print(f"📥 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            content = result["choices"][0]["message"]["content"]
            print(f"✅ Success! Response: {content}")
            return True
        else:
            print(f"❌ Error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_manga_extraction_format():
    """Test how manga_extraction.py creates base64 images"""
    print("\n🔍 Testing manga extraction image format...")
    
    try:
        # Import the manga extraction module
        import manga_extraction
        
        # Check if there's a sample PDF to test with
        import os
        pdf_path = "naruto/v10/v10.pdf"
        
        if os.path.exists(pdf_path):
            print(f"📖 Found sample PDF: {pdf_path}")
            
            # Extract pages from PDF
            pages_data = manga_extraction.extract_all_pages_as_images(pdf_path)
            pages = pages_data["scaled"]  # Use scaled images
            
            if pages:
                first_page = pages[0]
                print(f"🔍 Extracted page base64 length: {len(first_page)} characters")
                print(f"🔍 Base64 starts with: {first_page[:50]}...")
                
                # Test this with LM Studio
                payload = {
                    "model": QWEN_VISION_MODEL,
                    "messages": [
                        { 
                            "role": "user",
                            "content": [
                                {
                                    "type": "text",
                                    "text": "What do you see in this manga page? Just give a brief description."
                                },
                                {
                                    "type": "image_url",
                                    "image_url": { 
                                        "url": f"data:image/png;base64,{first_page}" 
                                    }
                                }
                            ]
                        }
                    ],
                    "temperature": 0.1,
                    "max_tokens": 200,
                    "stream": False
                }
                
                response = requests.post(
                    f"{LM_STUDIO_URL}/v1/chat/completions",
                    headers={"Content-Type": "application/json"},
                    json=payload,
                    timeout=60
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    print(f"✅ Manga page analysis successful!")
                    print(f"📝 Response: {content[:100]}...")
                    return True
                else:
                    print(f"❌ Manga page analysis failed: {response.status_code}")
                    print(f"📄 Response: {response.text}")
                    return False
            else:
                print("❌ No pages extracted from PDF")
                return False
        else:
            print(f"⚠️  Sample PDF not found: {pdf_path}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing manga extraction: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing Image Format for LM Studio\n")
    
    # Test 1: Simple test image
    test1_success = test_exact_lm_studio_format()
    
    # Test 2: Real manga page
    test2_success = test_manga_extraction_format()
    
    print(f"\n📊 Test Results:")
    print(f"✅ Simple image test: {'PASSED' if test1_success else 'FAILED'}")
    print(f"✅ Manga page test: {'PASSED' if test2_success else 'FAILED'}")
    
    if test1_success and test2_success:
        print("\n🎉 All image format tests passed! LM Studio integration is working correctly.")
    else:
        print("\n❌ Some tests failed. Check the error messages above.")
