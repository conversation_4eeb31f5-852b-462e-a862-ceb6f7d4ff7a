import json
import requests
import base64
import os

# Free with LM Studio - no token costs!
VISION_PRICE_PER_TOKEN = 0.0
GPT_3_5_TURBO_PRICE_PER_TOKEN = 0.0

# LM Studio configuration
LM_STUDIO_URL = "http://localhost:1234"
QWEN_VISION_MODEL = "qwen2-vl-2b-instruct"  # Updated to match your actual model


def analyze_images_with_qwen_vision(
    character_profiles, pages, client, prompt, instructions, detail="low"
):
    """
    Analyze images using LM Studio's Qwen vision model via HTTP API
    """
    try:
        # Construct the messages for LM Studio API (same format as OpenAI)
        messages = [
            {"role": "system", "content": instructions}
        ]

        # Add character profile reference message with images
        if character_profiles:
            profile_content = [
                {
                    "type": "text",
                    "text": "Here are some character profile pages, for your reference:",
                }
            ]
            # Add character profile images
            for img_base64 in character_profiles:
                profile_content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_base64}"
                    }
                })

            messages.append({
                "role": "user",
                "content": profile_content
            })

        # Add main prompt with manga pages
        main_content = [{"type": "text", "text": prompt}]

        # Add manga page images
        for img_base64 in pages:
            main_content.append({
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/png;base64,{img_base64}"
                }
            })

        messages.append({
            "role": "user",
            "content": main_content
        })

        # Prepare the request payload for LM Studio
        payload = {
            "model": QWEN_VISION_MODEL,
            "messages": messages,
            "temperature": 0.1,  # Lower temperature for more consistent JSON output
            "max_tokens": 4096,
            "stream": False
        }

        # Make the request to LM Studio
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"{LM_STUDIO_URL}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=300  # 5 minute timeout for vision processing
        )

        if response.status_code != 200:
            raise Exception(f"LM Studio API error: {response.status_code} - {response.text}")

        response_data = response.json()

        # Create a response object similar to OpenAI's format for compatibility
        class MockResponse:
            def __init__(self, response_data):
                self.choices = [MockChoice(response_data)]
                self.usage = MockUsage()

        class MockChoice:
            def __init__(self, response_data):
                self.message = MockMessage(response_data["choices"][0]["message"]["content"])

        class MockMessage:
            def __init__(self, content):
                self.content = content

        class MockUsage:
            def __init__(self):
                self.total_tokens = 0  # Free with LM Studio

        return MockResponse(response_data)

    except Exception as e:
        print(f"Error with LM Studio Qwen vision: {e}")
        print("Make sure LM Studio is running on http://localhost:1234 with a Qwen vision model loaded")
        print("You can check available models at: http://localhost:1234/v1/models")
        raise e

# Keep the old function name for backward compatibility
def analyze_images_with_gpt4_vision(
    character_profiles, pages, client, prompt, instructions, detail="low"
):
    """Backward compatibility wrapper"""
    return analyze_images_with_qwen_vision(
        character_profiles, pages, client, prompt, instructions, detail
    )


def detect_important_pages(
    profile_reference,
    chapter_reference,
    pages,
    client,
    prompt,
    instructions,
    detail="low",
):
    # Construct the messages including the prompt and images
    messages = [
        {"role": "system", "content": instructions},
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Here are some character profile pages, for your reference:",
                }
            ]
            + [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_base64}",
                        "detail": detail,
                    },
                }
                for img_base64 in profile_reference
            ],
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Here are some chapter start pages, for your reference:",
                }
            ]
            + [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_base64}",
                        "detail": detail,
                    },
                }
                for img_base64 in chapter_reference
            ],
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": prompt}]
            + [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_base64}",
                        "detail": detail,
                    },
                }
                for img_base64 in pages
            ],
        },
    ]

    # Use LM Studio instead of OpenAI
    payload = {
        "model": QWEN_VISION_MODEL,
        "messages": messages,
        "temperature": 0.1,  # Lower temperature for more consistent JSON output
        "max_tokens": 4096,
        "stream": False
    }

    headers = {"Content-Type": "application/json"}
    response = requests.post(
        f"{LM_STUDIO_URL}/v1/chat/completions",
        headers=headers,
        json=payload,
        timeout=300
    )

    if response.status_code != 200:
        raise Exception(f"LM Studio API error: {response.status_code} - {response.text}")

    response_data = response.json()
    response_text = response_data["choices"][0]["message"]["content"]
    tokens = 0  # Free with LM Studio

    # parse response text json from response.choices[0].message.content into object
    try:
        # Extract the text content from the first choice's message (if structured as expected)
        parsed_response = json.loads(response_text)
        if "important_pages" not in parsed_response:
            print("Warning: Response doesn't contain 'important_pages' field")
            parsed_response = {"important_pages": []}
    except (AttributeError, IndexError, json.JSONDecodeError) as e:
        # Handle cases where parsing fails - try to extract JSON from response
        print(f"JSON parsing failed, attempting to extract JSON from response...")
        print(f"Raw response: {response_text[:200]}...")

        # Try to extract JSON from markdown code blocks first
        import re

        # Look for JSON in markdown code blocks
        markdown_json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
        if markdown_json_match:
            try:
                parsed_response = json.loads(markdown_json_match.group(1))
                print("Successfully extracted JSON from markdown code block")
            except json.JSONDecodeError:
                parsed_response = None
        else:
            # Try to find JSON in the response text
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    parsed_response = json.loads(json_match.group())
                    print("Successfully extracted JSON from response")
                except json.JSONDecodeError:
                    parsed_response = None
            else:
                parsed_response = None

        if parsed_response is None:
            print("Failed to parse any JSON, using empty result")
            parsed_response = {"important_pages": []}

    return {
        "total_tokens": tokens,
        "parsed_response": parsed_response.get("important_pages", []),
    }


def completions(client, text, prompt):
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": text},  # Simplified for LM Studio
    ]

    # Use LM Studio instead of OpenAI
    payload = {
        "model": QWEN_VISION_MODEL,
        "messages": messages,
        "temperature": 0.1,  # Lower temperature for more consistent JSON output
        "max_tokens": 4096,
        "stream": False
    }

    headers = {"Content-Type": "application/json"}
    response = requests.post(
        f"{LM_STUDIO_URL}/v1/chat/completions",
        headers=headers,
        json=payload,
        timeout=300
    )

    if response.status_code != 200:
        raise Exception(f"LM Studio API error: {response.status_code} - {response.text}")

    response_data = response.json()

    # Create mock response object for compatibility
    class MockResponse:
        def __init__(self, response_data):
            self.choices = [MockChoice(response_data)]
            self.usage = MockUsage()

    class MockChoice:
        def __init__(self, response_data):
            self.message = MockMessage(response_data["choices"][0]["message"]["content"])

    class MockMessage:
        def __init__(self, content):
            self.content = content

    class MockUsage:
        def __init__(self):
            self.total_tokens = 0  # Free with LM Studio

    return MockResponse(response_data)


def get_important_panels(
    profile_reference, panels, client, prompt, instructions, detail="low"
):
    # Construct the messages including the prompt and images
    messages = [
        {"role": "system", "content": instructions},
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "Here are some character profile pages, for your reference:",
                }
            ]
            + [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_base64}",
                        "detail": detail,
                    },
                }
                for img_base64 in profile_reference
            ],
        },
        {
            "role": "user",
            "content": [{"type": "text", "text": prompt}]
            + [
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{img_base64}",
                        "detail": detail,
                    },
                }
                for img_base64 in panels
            ],
        },
    ]
    try:
        # Use LM Studio instead of OpenAI
        payload = {
            "model": QWEN_VISION_MODEL,
            "messages": messages,
            "temperature": 0.7,
            "max_tokens": 4096,
            "stream": False
        }

        headers = {"Content-Type": "application/json"}
        response = requests.post(
            f"{LM_STUDIO_URL}/v1/chat/completions",
            headers=headers,
            json=payload,
            timeout=300
        )

        if response.status_code != 200:
            raise Exception(f"LM Studio API error: {response.status_code} - {response.text}")

        response_data = response.json()
        response_text = response_data["choices"][0]["message"]["content"]

    except Exception as e:
        print(f"Error with LM Studio: {e}")
        return {"total_tokens": 0, "parsed_response": []}

    print("QWEN RESPONSE:", response_text)
    tokens = 0  # Free with LM Studio

    # parse response text json from response.choices[0].message.content into object
    try:
        # Extract the text content from the first choice's message (if structured as expected)
        parsed_response = json.loads(response_text)
        if "important_panels" not in parsed_response:
            print("Warning: Response doesn't contain 'important_panels' field")
            parsed_response = {"important_panels": []}
    except (AttributeError, IndexError, json.JSONDecodeError) as e:
        # Handle cases where parsing fails - try to extract JSON from response
        print(f"JSON parsing failed, attempting to extract JSON from response...")
        print(f"Raw response: {response_text[:200]}...")

        # Try to extract JSON from markdown code blocks first
        import re

        # Look for JSON in markdown code blocks
        markdown_json_match = re.search(r'```json\s*(\{.*?\})\s*```', response_text, re.DOTALL)
        if markdown_json_match:
            try:
                parsed_response = json.loads(markdown_json_match.group(1))
                print("Successfully extracted JSON from markdown code block")
            except json.JSONDecodeError:
                parsed_response = None
        else:
            # Try to find JSON in the response text
            json_match = re.search(r'\{.*\}', response_text, re.DOTALL)
            if json_match:
                try:
                    parsed_response = json.loads(json_match.group())
                    print("Successfully extracted JSON from response")
                except json.JSONDecodeError:
                    parsed_response = None
            else:
                parsed_response = None

        if parsed_response is None:
            print("Failed to parse any JSON, using empty result")
            parsed_response = {"important_panels": []}

    return {
        "total_tokens": tokens,
        "parsed_response": parsed_response.get("important_panels", []),
    }


JSON_PARSE_PROMPT = """
You are a JSON parser. Return a properly formatted json object based on the input from the user.
Your response must be in the following format:
{"important_pages": Array<{"image_index": int 0-19, "type": "profile" | "chapter"}>}

Examples of valid responses:
```
{
    "important_pages": [
        {"image_index": 0, "type": "profile"},
        {"image_index": 17, "type": "chapter"}
    ]
}
```

```
{
    "important_pages": []
}
```

"""


JSON_PARSE_PROMPT_PANELS = """
You are a JSON parser. Return a properly formatted json object based on the input from the user.
Your response must be in the following format:
{"important_panels": Array<int>}

Examples of valid responses:
```
{
    "important_panels": [
        0, 4,
    ]
}
```

```
{
    "important_panels": []
}
```

"""
