# Manga Recap with FREE AI Vision & TTS 🎉

## Project Overview

This project generates summaries of manga volumes by analyzing images extracted from PDF files using **completely FREE AI services**! It uses **LM Studio with Qwen vision models** (free local inference) to understand manga content and **Microsoft Edge-TTS** (free text-to-speech) for narration. No API keys or paid services required!

The project processes PDFs to extract images, analyzes them with local AI vision models, generates compelling story summaries, creates natural-sounding narration, and combines everything into video recaps.

Join the Discord: https://discord.gg/MMqcuDe2WZ

https://github.com/pashpashpash/manga-reader/assets/20898225/debb0c15-3579-477c-813d-2ed878b0e6ea

## Features ✨

- 🆓 **100% FREE** - No API keys or paid services required!
- 🖼️ PDF processing to extract manga pages and panels
- 🤖 **LM Studio + Qwen Vision** - Local AI vision analysis (free)
- 🗣️ **Microsoft Edge-TTS** - High-quality, natural text-to-speech (free)
- 📖 Story-telling tone summaries of manga volumes
- 🎬 Automatic video creation with narration and panels
- 🎯 Smart panel and page selection for optimal storytelling

## Prerequisites

Before you begin, ensure you have:

- **Python 3.8+** (required for modern dependencies)
- **LM Studio** installed and running
- **A Qwen vision model** downloaded in LM Studio (e.g., `qwen2-vl-7b-instruct`)
- Pip3 (Python package manager)
- Virtual environment (recommended)

## Installation Steps

### Step 1: Set Up LM Studio 🤖

1. **Download and Install LM Studio**
   - Visit [https://lmstudio.ai/](https://lmstudio.ai/)
   - Download and install LM Studio for your operating system

2. **Download a Qwen Vision Model**
   - Open LM Studio
   - Go to the "Discover" tab
   - Search for "qwen2-vl" or "qwen vision"
   - Download a model like `qwen2-vl-7b-instruct` (recommended)
   - Wait for the download to complete

3. **Start the LM Studio Server**
   - In LM Studio, go to the "Local Server" tab
   - Load your downloaded Qwen vision model
   - Click "Start Server"
   - Ensure it's running on `http://localhost:1234`

### Step 2: Set Up Python Environment 🐍

1. **Create a virtual environment**
```bash
python -m venv venv
```

2. **Activate the virtual environment**

Windows:
```bash
venv\Scripts\activate
```

macOS/Linux:
```bash
source venv/bin/activate
```

3. **Install Required Python Packages**
```bash
pip install -r requirements.txt
```

### Step 3: Configure Environment (Optional) ⚙️

Create a `.env` file in the root directory (optional - defaults work fine):

```env
# LM Studio Configuration (defaults)
LM_STUDIO_URL=http://localhost:1234
QWEN_VISION_MODEL=qwen2-vl-7b-instruct

# Edge-TTS Voice (optional - can be changed in code)
EDGE_TTS_VOICE=en-US-AriaNeural
```

### Step 4: Prepare Your Manga PDFs 📚

Place your manga volume PDF files in this directory structure:
```
manga-name/
├── v10/
│   └── v10.pdf
├── chapter-reference.pdf
└── profile-reference.pdf
```

For example: `naruto/v10/v10.pdf`, `naruto/chapter-reference.pdf`, `naruto/profile-reference.pdf`

The reference files help the AI identify:
- **chapter-reference.pdf**: Chapter start pages for proper segmentation
- **profile-reference.pdf**: Character introductions for better recognition

## Running the Project 🚀

### Quick Start
```bash
python app.py --manga naruto --volume-number 10
```

### Available Options
```bash
# Generate text summary only (no video/audio)
python app.py --manga naruto --volume-number 10 --text-only

# Full video generation with narration
python app.py --manga naruto --volume-number 10
```

### What Happens:
1. 📖 Extracts pages and panels from your PDF
2. 🤖 Analyzes content with local Qwen vision model (FREE!)
3. ✍️ Generates compelling story summaries
4. 🗣️ Creates natural narration with Edge-TTS (FREE!)
5. 🎬 Combines everything into a video recap
6. 💾 Saves the final video as `manga-name/v##/recap.mp4`

### Cost Breakdown 💰
**Total Cost: $0.00** 🎉
- Vision Analysis: FREE (LM Studio local inference)
- Text-to-Speech: FREE (Microsoft Edge-TTS)
- No API keys required!

## Troubleshooting 🔧

### LM Studio Issues
- Ensure LM Studio server is running on `http://localhost:1234`
- Check that a Qwen vision model is loaded
- Verify the model name matches your configuration

### Audio Issues
- Edge-TTS works offline and requires no setup
- If audio generation fails, check your internet connection (needed for voice synthesis)

### Performance Tips
- Use a GPU-enabled Qwen model for faster processing
- Larger models (7B+) provide better analysis quality
- Consider using quantized models for lower memory usage

## Alternative Setup (Jupyter Notebook) 📓

For step-by-step execution and debugging, use the Jupyter notebook:
```bash
jupyter notebook anime_recap.ipynb
```

This allows you to run the process cell by cell, perfect for understanding and customizing the workflow.

## Voice Options 🎤

Edge-TTS supports many high-quality voices. You can change the voice in `movie_director.py`:

**Popular English Voices:**
- `en-US-AriaNeural` - Female, clear and natural (default)
- `en-US-GuyNeural` - Male, warm and natural
- `en-US-JennyNeural` - Female, friendly
- `en-US-DavisNeural` - Male, confident
- `en-US-JaneNeural` - Female, professional
- `en-US-JasonNeural` - Male, casual

**Other Languages Available:**
- Spanish: `es-ES-ElviraNeural`, `es-MX-DaliaNeural`
- French: `fr-FR-DeniseNeural`, `fr-CA-SylvieNeural`
- Japanese: `ja-JP-NanamiNeural`, `ja-JP-KeitaNeural`
- And many more!

## Technical Details 🔧

### Architecture Changes
This project has been modified to use completely free alternatives:

**Original → Free Alternative**
- OpenAI GPT-4 Vision → LM Studio + Qwen Vision Models
- ElevenLabs TTS → Microsoft Edge-TTS
- Paid APIs → Local/Free services

### Model Recommendations
- **Qwen2-VL-7B-Instruct**: Best balance of quality and speed
- **Qwen2-VL-2B-Instruct**: Faster, lower memory usage
- **Qwen2.5-VL-7B-Instruct**: Latest version with improvements

### System Requirements
- **RAM**: 8GB+ (16GB recommended for 7B models)
- **Storage**: 5-15GB for model files
- **GPU**: Optional but recommended for faster inference

## Contributing 🤝

Contributions are welcome! This project now runs entirely on free services, making it accessible to everyone.

## License 📄

This project is open source. Check the LICENSE file for details.

## Support 💬

Join the Discord: https://discord.gg/MMqcuDe2WZ

---

**🎉 Enjoy creating manga recaps with completely FREE AI services!**
